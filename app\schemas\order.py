"""
销售订单相关的Pydantic模型
"""

from typing import List, Optional
from datetime import datetime
from decimal import Decimal
from pydantic import BaseModel, validator
from app.db.models import OrderStatus


class OrderItemBase(BaseModel):
    """订单明细基础模型"""
    rabbit_type_id: int
    quantity: Decimal
    unit: str
    unit_price: Decimal
    remark: Optional[str] = None

    @validator('quantity')
    def validate_quantity(cls, v):
        if v <= 0:
            raise ValueError('数量必须大于0')
        return v

    @validator('unit_price')
    def validate_unit_price(cls, v):
        if v <= 0:
            raise ValueError('单价必须大于0')
        return v

    @validator('unit')
    def validate_unit(cls, v):
        if not v or not v.strip():
            raise ValueError('单位不能为空')
        if len(v.strip()) > 10:
            raise ValueError('单位长度不能超过10个字符')
        return v.strip()


class OrderItemCreate(OrderItemBase):
    """创建订单明细模型"""
    pass


class OrderItemUpdate(BaseModel):
    """更新订单明细模型"""
    rabbit_type_id: Optional[int] = None
    quantity: Optional[Decimal] = None
    unit: Optional[str] = None
    unit_price: Optional[Decimal] = None
    remark: Optional[str] = None

    @validator('quantity')
    def validate_quantity(cls, v):
        if v is not None and v <= 0:
            raise ValueError('数量必须大于0')
        return v

    @validator('unit_price')
    def validate_unit_price(cls, v):
        if v is not None and v <= 0:
            raise ValueError('单价必须大于0')
        return v


class OrderItemResponse(BaseModel):
    """订单明细响应模型"""
    id: int
    rabbit_type_id: int
    rabbit_type_name: str
    quantity: Decimal
    unit: str
    unit_price: Decimal
    total_price: Decimal
    remark: Optional[str] = None

    class Config:
        from_attributes = True


class OrderBase(BaseModel):
    """订单基础模型"""
    customer_id: int
    salesperson_id: int
    amount_paid: Decimal = Decimal('0')
    payment_method: Optional[str] = None
    remark: Optional[str] = None

    @validator('amount_paid')
    def validate_amount_paid(cls, v):
        if v < 0:
            raise ValueError('已付金额不能为负数')
        return v

    @validator('payment_method')
    def validate_payment_method(cls, v):
        if v is not None and len(v.strip()) > 50:
            raise ValueError('付款方式长度不能超过50个字符')
        return v.strip() if v else None


class OrderCreate(OrderBase):
    """创建订单模型"""
    items: List[OrderItemCreate]
    created_at: Optional[datetime] = None  # 可选的创建时间，用于添加历史订单

    @validator('items')
    def validate_items(cls, v):
        if not v:
            raise ValueError('订单明细不能为空')
        if len(v) > 50:
            raise ValueError('订单明细不能超过50项')
        return v

    @validator('created_at')
    def validate_created_at(cls, v):
        if v is not None:
            # 不能是未来时间
            if v > datetime.now():
                raise ValueError('创建时间不能是未来时间')
            # 不能太久远（比如不能早于2020年）
            if v.year < 2020:
                raise ValueError('创建时间不能早于2020年')
        return v


class OrderUpdate(BaseModel):
    """更新订单模型"""
    customer_id: Optional[int] = None
    salesperson_id: Optional[int] = None
    amount_paid: Optional[Decimal] = None
    payment_method: Optional[str] = None
    status: Optional[OrderStatus] = None
    remark: Optional[str] = None

    @validator('amount_paid')
    def validate_amount_paid(cls, v):
        if v is not None and v < 0:
            raise ValueError('已付金额不能为负数')
        return v


class OrderResponse(BaseModel):
    """订单响应模型"""
    id: int
    order_number: str
    customer_id: int
    customer_name: str
    salesperson_id: int
    salesperson_name: str
    total_price: Decimal
    amount_paid: Decimal
    unpaid_amount: Decimal
    payment_method: Optional[str] = None
    status: OrderStatus
    status_display: str
    remark: Optional[str] = None
    items: List[OrderItemResponse]

    class Config:
        from_attributes = True


class OrderSummary(BaseModel):
    """订单摘要模型（列表用）"""
    id: int
    order_number: str
    customer_id: int
    customer_name: str
    salesperson_id: int
    salesperson_name: str
    total_price: Decimal
    amount_paid: Decimal
    unpaid_amount: Decimal
    status: OrderStatus
    status_display: str
    created_at: datetime
    item_count: int = 0

    class Config:
        from_attributes = True


def get_status_display(status: OrderStatus) -> str:
    """获取订单状态显示名称"""
    status_map = {
        OrderStatus.PENDING: "待付款",
        OrderStatus.PARTIAL_PAID: "部分付款",
        OrderStatus.PAID: "已付清",
        OrderStatus.DELETED: "已删除",
    }
    return status_map.get(status, "未知状态")
