#!/usr/bin/env python3
"""
测试API修复
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from app.db.database import SessionLocal
from app.schemas.order import OrderSummary, get_status_display
from app.db.models import Order, OrderStatus


def test_order_summary_creation():
    """测试OrderSummary创建"""
    print("🧪 测试OrderSummary创建...")
    
    db = SessionLocal()
    try:
        # 获取一个订单
        order = db.query(Order).first()
        if not order:
            print("❌ 没有找到订单数据")
            return False
        
        print(f"📋 测试订单: ID={order.id}, 编号={order.order_number}")
        
        # 尝试创建OrderSummary
        try:
            summary = OrderSummary(
                id=order.id,
                order_number=order.order_number,
                customer_id=order.customer_id,
                customer_name="测试客户",
                salesperson_id=order.salesperson_id,
                salesperson_name="测试销售员",
                total_price=order.total_price,
                amount_paid=order.amount_paid,
                unpaid_amount=order.unpaid_amount,
                status=order.status,
                status_display=get_status_display(order.status),
                created_at=order.created_at,
                item_count=len(order.items) if order.items else 0
            )
            
            print(f"✅ OrderSummary创建成功")
            print(f"   订单编号: {summary.order_number}")
            print(f"   状态: {summary.status.value} ({summary.status_display})")
            print(f"   商品数量: {summary.item_count}")
            return True
            
        except Exception as e:
            print(f"❌ OrderSummary创建失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        db.close()


def test_status_display():
    """测试状态显示"""
    print("\n🎨 测试状态显示...")
    
    try:
        statuses = [
            OrderStatus.PENDING,
            OrderStatus.PARTIAL_PAID,
            OrderStatus.PAID,
            OrderStatus.DELETED
        ]
        
        for status in statuses:
            display = get_status_display(status)
            print(f"   {status.value} -> {display}")
        
        print("✅ 状态显示测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 状态显示测试失败: {e}")
        return False


def test_database_status_values():
    """测试数据库中的状态值"""
    print("\n🗄️ 测试数据库状态值...")
    
    db = SessionLocal()
    try:
        # 检查数据库中的状态值
        result = db.execute(text("""
            SELECT DISTINCT status, COUNT(*) as count 
            FROM orders 
            GROUP BY status 
            ORDER BY status
        """))
        
        status_counts = result.fetchall()
        
        print("📊 数据库中的状态分布:")
        for status, count in status_counts:
            try:
                # 尝试转换为枚举
                status_enum = OrderStatus(status)
                display = get_status_display(status_enum)
                print(f"   {status} ({display}): {count} 条")
            except ValueError:
                print(f"   ❌ 未知状态 {status}: {count} 条")
        
        print("✅ 数据库状态值测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据库状态值测试失败: {e}")
        return False
    finally:
        db.close()


def main():
    """主函数"""
    print("🚀 API修复测试")
    print("=" * 50)
    
    tests = [
        ("OrderSummary创建", test_order_summary_creation),
        ("状态显示", test_status_display),
        ("数据库状态值", test_database_status_values),
    ]
    
    success_count = 0
    for test_name, test_func in tests:
        print(f"🧪 运行测试: {test_name}")
        if test_func():
            success_count += 1
        print()
    
    # 总结
    print("=" * 50)
    print(f"📊 测试结果: {success_count}/{len(tests)} 通过")
    
    if success_count == len(tests):
        print("🎉 所有测试通过！API修复成功")
        print("\n✅ 修复内容:")
        print("   - 订单状态枚举值匹配")
        print("   - OrderSummary包含order_number字段")
        print("   - 状态显示函数正常")
        print("   - 数据库状态值正确")
    else:
        print("❌ 部分测试失败，需要进一步检查")
    
    return success_count == len(tests)


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
