#!/usr/bin/env python3
"""
更新订单编号格式
从 DD20241201000001 改为 20241201001
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from app.db.database import SessionLocal


def update_order_number_format(db):
    """更新订单编号格式"""
    print("📋 更新订单编号格式...")
    print("   从: DD20241201000001")
    print("   到: 20241201001")
    
    try:
        # 获取所有需要更新的订单
        result = db.execute(text("""
            SELECT id, order_number, created_at 
            FROM orders 
            WHERE order_number IS NOT NULL 
            ORDER BY created_at ASC
        """))
        
        orders = result.fetchall()
        
        if not orders:
            print("✅ 没有需要更新的订单")
            return True
        
        print(f"📊 发现 {len(orders)} 个订单需要更新编号")
        
        # 按日期分组重新生成编号
        date_counters = {}
        updated_count = 0
        
        for order_id, old_number, created_at in orders:
            # 获取订单日期
            order_date = created_at.date() if created_at else datetime.now().date()
            date_str = order_date.strftime("%Y%m%d")
            
            # 初始化或递增当日计数器
            if date_str not in date_counters:
                date_counters[date_str] = 0
            
            date_counters[date_str] += 1
            
            # 生成新的订单编号
            sequence = f"{date_counters[date_str]:03d}"
            new_order_number = f"{date_str}{sequence}"
            
            # 确保唯一性
            while True:
                check_result = db.execute(text("""
                    SELECT COUNT(*) FROM orders 
                    WHERE order_number = :order_number AND id != :order_id
                """), {"order_number": new_order_number, "order_id": order_id}).scalar()
                
                if check_result == 0:
                    break
                
                date_counters[date_str] += 1
                sequence = f"{date_counters[date_str]:03d}"
                new_order_number = f"{date_str}{sequence}"
            
            # 更新订单编号
            db.execute(text("""
                UPDATE orders SET order_number = :new_number WHERE id = :order_id
            """), {"new_number": new_order_number, "order_id": order_id})
            
            updated_count += 1
            
            if updated_count % 10 == 0:
                print(f"   已处理 {updated_count}/{len(orders)} 个订单...")
            
            # 显示前几个更新示例
            if updated_count <= 5:
                print(f"   {old_number} -> {new_order_number}")
        
        db.commit()
        print(f"✅ 成功更新 {updated_count} 个订单编号")
        return True
        
    except Exception as e:
        print(f"❌ 更新订单编号失败: {e}")
        db.rollback()
        return False


def verify_new_format(db):
    """验证新格式"""
    print("\n🔍 验证新订单编号格式...")
    
    try:
        # 检查所有订单编号格式
        result = db.execute(text("""
            SELECT order_number, COUNT(*) as count
            FROM orders 
            WHERE order_number IS NOT NULL
            GROUP BY order_number
            HAVING COUNT(*) > 1
        """))
        
        duplicates = result.fetchall()
        
        if duplicates:
            print(f"❌ 发现 {len(duplicates)} 个重复编号:")
            for number, count in duplicates:
                print(f"   {number}: {count} 次")
            return False
        else:
            print("✅ 所有订单编号都是唯一的")
        
        # 检查编号格式
        result = db.execute(text("""
            SELECT order_number 
            FROM orders 
            WHERE order_number IS NOT NULL 
            AND order_number NOT REGEXP '^[0-9]{8}[0-9]{3}$'
            LIMIT 5
        """))
        
        invalid_formats = result.fetchall()
        
        if invalid_formats:
            print(f"❌ 发现 {len(invalid_formats)} 个格式错误的订单编号:")
            for (number,) in invalid_formats:
                print(f"   {number}")
            return False
        else:
            print("✅ 所有订单编号格式正确 (YYYYMMDDXXX)")
        
        # 显示一些示例
        result = db.execute(text("""
            SELECT order_number, created_at 
            FROM orders 
            WHERE order_number IS NOT NULL 
            ORDER BY created_at DESC 
            LIMIT 5
        """))
        
        examples = result.fetchall()
        print("\n📋 新格式示例:")
        for number, created_at in examples:
            date_part = number[:8]
            sequence_part = number[8:]
            formatted_date = f"{date_part[:4]}-{date_part[4:6]}-{date_part[6:8]}"
            print(f"   {number} -> {formatted_date} 第{int(sequence_part)}单")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


def show_statistics(db):
    """显示统计信息"""
    print("\n📊 订单编号统计:")
    
    try:
        # 按日期统计订单数量
        result = db.execute(text("""
            SELECT 
                SUBSTRING(order_number, 1, 8) as date_part,
                COUNT(*) as count
            FROM orders 
            WHERE order_number IS NOT NULL
            GROUP BY SUBSTRING(order_number, 1, 8)
            ORDER BY date_part DESC
            LIMIT 10
        """))
        
        daily_stats = result.fetchall()
        
        for date_part, count in daily_stats:
            formatted_date = f"{date_part[:4]}-{date_part[4:6]}-{date_part[6:8]}"
            print(f"   {formatted_date}: {count} 单")
        
        # 总计
        result = db.execute(text("""
            SELECT COUNT(*) FROM orders WHERE order_number IS NOT NULL
        """))
        total = result.scalar()
        print(f"\n   总计: {total} 单")
        
    except Exception as e:
        print(f"❌ 统计失败: {e}")


def main():
    """主函数"""
    print("🚀 订单编号格式更新")
    print("=" * 50)
    
    db = SessionLocal()
    try:
        # 检查数据库连接
        db.execute(text("SELECT 1"))
        print("✅ 数据库连接正常\n")
        
        # 更新订单编号格式
        if update_order_number_format(db):
            # 验证新格式
            if verify_new_format(db):
                # 显示统计信息
                show_statistics(db)
                
                print("\n" + "=" * 50)
                print("🎉 订单编号格式更新完成！")
                print("\n📋 新格式特点:")
                print("   ✅ 格式: YYYYMMDDXXX (11位)")
                print("   ✅ 含义: 销售日期 + 当天序号")
                print("   ✅ 示例: 20241201001 (2024年12月1日第1单)")
                print("   ✅ 简洁: 去掉无意义的前缀")
                print("   ✅ 直观: 一眼看出销售日期")
                
                print("\n🔗 编号解析:")
                print("   20241201001 = 2024-12-01 第1单")
                print("   20241201015 = 2024-12-01 第15单")
                print("   20241202001 = 2024-12-02 第1单")
                
            else:
                print("\n❌ 格式验证失败")
        else:
            print("\n❌ 订单编号更新失败")
            
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False
    finally:
        db.close()
    
    return True


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
