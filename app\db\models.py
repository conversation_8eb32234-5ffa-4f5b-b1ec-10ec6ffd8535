"""
数据库模型定义
"""

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Integer, String, DateTime, Enum, Text, ForeignKey, Numeric
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum

Base = declarative_base()


class UserRole(enum.Enum):
    """用户角色枚举"""
    SUPER_ADMIN = "super_admin"      # 超级管理员
    ADMIN = "admin"                  # 普通管理员
    BREEDER = "breeder"              # 饲养员
    SALESPERSON = "salesperson"      # 销售员
    USER = "user"                    # 普通用户


class User(Base):
    """用户模型"""
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    phone = Column(String(11), unique=True, index=True, nullable=False, comment="手机号")
    username = Column(String(50), nullable=False, comment="用户名")
    hashed_password = Column(String(255), nullable=False, comment="加密密码")
    role = Column(Enum(UserRole), default=UserRole.USER, nullable=False, comment="用户角色")
    is_active = Column(Boolean, default=True, comment="是否激活")
    is_superuser = Column(Boolean, default=False, comment="是否超级用户")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")


class Customer(Base):
    """客户模型"""
    __tablename__ = "customers"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, comment="姓名或单位名称")
    phone = Column(String(20), nullable=False, comment="联系方式")
    address = Column(String(255), nullable=True, comment="地址")
    remark = Column(Text, nullable=True, comment="备注")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")


class RabbitType(Base):
    """兔子类型模型"""
    __tablename__ = "rabbit_types"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), nullable=False, unique=True, comment="类型名称")
    default_unit = Column(String(10), nullable=True, comment="默认单位：只、斤、公斤等")
    default_price = Column(Numeric(10, 2), nullable=True, comment="默认单价（元）")
    remark = Column(Text, nullable=True, comment="备注，例如用途、品种说明")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")


class OrderStatus(enum.Enum):
    """订单状态枚举"""
    PENDING = "pending"          # 待付款
    PARTIAL_PAID = "partial_paid"  # 部分付款
    PAID = "paid"               # 已付清
    DELETED = "deleted"          # 已删除（仅超级管理员可操作）


class Order(Base):
    """销售订单主表"""
    __tablename__ = "orders"

    id = Column(Integer, primary_key=True, index=True)
    order_number = Column(String(32), unique=True, nullable=False, index=True, comment="订单编号")
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=False, comment="客户ID")
    salesperson_id = Column(Integer, ForeignKey("users.id"), nullable=False, comment="销售员ID")
    total_price = Column(Numeric(10, 2), nullable=False, comment="订单总价")
    amount_paid = Column(Numeric(10, 2), default=0, comment="已付金额")
    unpaid_amount = Column(Numeric(10, 2), nullable=False, comment="未付金额")
    payment_method = Column(String(50), nullable=True, comment="付款方式")
    status = Column(Enum(OrderStatus), default=OrderStatus.PENDING, nullable=False, comment="订单状态")
    remark = Column(Text, nullable=True, comment="订单备注")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关联关系
    customer = relationship("Customer", backref="orders")
    salesperson = relationship("User", backref="sales_orders")
    items = relationship("OrderItem", back_populates="order", cascade="all, delete-orphan")


class OrderItem(Base):
    """订单明细表"""
    __tablename__ = "order_items"

    id = Column(Integer, primary_key=True, index=True)
    order_id = Column(Integer, ForeignKey("orders.id"), nullable=False, comment="订单ID")
    rabbit_type_id = Column(Integer, ForeignKey("rabbit_types.id"), nullable=False, comment="兔子类型ID")
    quantity = Column(Numeric(10, 2), nullable=False, comment="数量")
    unit = Column(String(10), nullable=False, comment="单位（只、斤等）")
    unit_price = Column(Numeric(10, 2), nullable=False, comment="单价")
    total_price = Column(Numeric(10, 2), nullable=False, comment="小计金额")
    remark = Column(Text, nullable=True, comment="明细备注")

    # 关联关系
    order = relationship("Order", back_populates="items")
    rabbit_type = relationship("RabbitType", backref="order_items")

    def __repr__(self):
        return f"<User(id={self.id}, phone={self.phone}, username={self.username})>"
