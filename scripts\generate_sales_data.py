#!/usr/bin/env python3
"""
生成40条模拟销售记录数据
"""

import sys
import os
import random
from datetime import datetime, timedelta
from decimal import Decimal

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text, func
from app.db.database import SessionLocal
from app.db.models import User, Customer, RabbitType, Order, OrderItem, OrderStatus, UserRole
from app.schemas.order import OrderCreate, OrderItemCreate


def get_or_create_customers(db):
    """获取或创建客户数据"""
    customers = db.query(Customer).all()
    if len(customers) < 20:
        print("📋 创建客户数据...")
        customer_names = [
            "张三", "李四", "王五", "赵六", "钱七", "孙八", "周九", "吴十",
            "郑十一", "王十二", "冯十三", "陈十四", "褚十五", "卫十六", "蒋十七", "沈十八",
            "韩十九", "杨二十", "朱二十一", "秦二十二", "尤二十三", "许二十四", "何二十五", "吕二十六",
            "施二十七", "张二十八", "孔二十九", "曹三十", "严三十一", "华三十二"
        ]
        
        addresses = [
            "北京市朝阳区", "上海市浦东新区", "广州市天河区", "深圳市南山区", "杭州市西湖区",
            "成都市锦江区", "武汉市武昌区", "西安市雁塔区", "南京市鼓楼区", "重庆市渝中区",
            "天津市和平区", "苏州市姑苏区", "青岛市市南区", "大连市中山区", "宁波市海曙区",
            "厦门市思明区", "福州市鼓楼区", "济南市历下区", "长沙市岳麓区", "郑州市金水区"
        ]
        
        for i, name in enumerate(customer_names[:20]):
            if i >= len(customers):
                customer = Customer(
                    name=name,
                    phone=f"1{random.randint(3,9)}{random.randint(100000000,999999999)}",
                    address=random.choice(addresses),
                    remark=f"客户{i+1}的备注信息"
                )
                db.add(customer)
        
        db.commit()
        customers = db.query(Customer).all()
    
    return customers


def get_or_create_rabbit_types(db):
    """获取或创建兔子类型数据"""
    rabbit_types = db.query(RabbitType).all()
    if len(rabbit_types) < 8:
        print("📋 创建兔子类型数据...")
        types_data = [
            {"name": "新西兰白兔", "description": "肉兔品种，生长快", "default_unit": "只", "default_price": Decimal("35.00")},
            {"name": "加利福尼亚兔", "description": "优质肉兔", "default_unit": "只", "default_price": Decimal("32.00")},
            {"name": "比利时兔", "description": "大型肉兔", "default_unit": "只", "default_price": Decimal("40.00")},
            {"name": "安哥拉兔", "description": "毛用兔", "default_unit": "只", "default_price": Decimal("45.00")},
            {"name": "肉兔(按斤)", "description": "商品肉兔", "default_unit": "斤", "default_price": Decimal("18.00")},
            {"name": "种兔(公)", "description": "种用公兔", "default_unit": "只", "default_price": Decimal("80.00")},
            {"name": "种兔(母)", "description": "种用母兔", "default_unit": "只", "default_price": Decimal("75.00")},
            {"name": "幼兔", "description": "2-3月龄幼兔", "default_unit": "只", "default_price": Decimal("25.00")},
        ]
        
        for type_data in types_data:
            if not db.query(RabbitType).filter(RabbitType.name == type_data["name"]).first():
                rabbit_type = RabbitType(**type_data)
                db.add(rabbit_type)
        
        db.commit()
        rabbit_types = db.query(RabbitType).all()
    
    return rabbit_types


def get_salespersons(db):
    """获取销售员用户"""
    salespersons = db.query(User).filter(
        User.role.in_([UserRole.SALESPERSON, UserRole.ADMIN, UserRole.SUPER_ADMIN])
    ).all()
    
    if not salespersons:
        print("⚠️ 没有找到销售员用户，使用所有用户")
        salespersons = db.query(User).all()
    
    return salespersons


def generate_sales_records(db, count=40):
    """生成销售记录"""
    print(f"🛒 开始生成{count}条销售记录...")
    
    # 获取基础数据
    customers = get_or_create_customers(db)
    rabbit_types = get_or_create_rabbit_types(db)
    salespersons = get_salespersons(db)
    
    if not customers or not rabbit_types or not salespersons:
        print("❌ 缺少基础数据，无法生成销售记录")
        return
    
    print(f"📊 基础数据统计:")
    print(f"   客户数量: {len(customers)}")
    print(f"   兔子类型: {len(rabbit_types)}")
    print(f"   销售员: {len(salespersons)}")
    
    # 付款方式选项
    payment_methods = ["现金", "微信", "支付宝", "银行转账", "刷卡", "赊账"]
    
    # 订单状态权重（更多已付清的订单）
    status_choices = [
        (OrderStatus.PAID, 0.6),      # 60% 已付清
        (OrderStatus.PARTIAL_PAID, 0.25),  # 25% 部分付款
        (OrderStatus.PENDING, 0.1),   # 10% 待付款
        (OrderStatus.CANCELLED, 0.05) # 5% 已取消
    ]
    
    # 生成时间范围（最近6个月）
    end_date = datetime.now()
    start_date = end_date - timedelta(days=180)
    
    created_orders = 0
    
    for i in range(count):
        try:
            # 随机选择基础数据
            customer = random.choice(customers)
            salesperson = random.choice(salespersons)
            
            # 随机生成订单时间
            random_days = random.randint(0, 180)
            order_date = start_date + timedelta(days=random_days)
            
            # 随机选择订单状态
            status_rand = random.random()
            cumulative_prob = 0
            order_status = OrderStatus.PAID
            for status, prob in status_choices:
                cumulative_prob += prob
                if status_rand <= cumulative_prob:
                    order_status = status
                    break
            
            # 生成订单明细（1-4项）
            items = []
            item_count = random.randint(1, 4)
            
            for _ in range(item_count):
                rabbit_type = random.choice(rabbit_types)
                
                # 根据兔子类型生成数量和价格
                unit = rabbit_type.default_unit or "只"
                if unit == "只":
                    quantity = Decimal(str(random.randint(1, 20)))
                    # 价格在默认价格基础上浮动±20%
                    price_variation = random.uniform(0.8, 1.2)
                    base_price = rabbit_type.default_price or Decimal("30.00")
                    unit_price = base_price * Decimal(str(price_variation))
                else:  # 按斤
                    quantity = Decimal(str(round(random.uniform(5.0, 50.0), 1)))
                    price_variation = random.uniform(0.8, 1.2)
                    base_price = rabbit_type.default_price or Decimal("18.00")
                    unit_price = base_price * Decimal(str(price_variation))
                
                # 保留2位小数
                unit_price = unit_price.quantize(Decimal('0.01'))
                
                item = OrderItemCreate(
                    rabbit_type_id=rabbit_type.id,
                    quantity=quantity,
                    unit=unit,
                    unit_price=unit_price,
                    remark=f"批次{random.randint(1, 10)}" if random.random() > 0.7 else None
                )
                items.append(item)
            
            # 计算总价
            total_price = sum(item.quantity * item.unit_price for item in items)
            
            # 根据订单状态确定已付金额
            if order_status == OrderStatus.PAID:
                amount_paid = total_price
            elif order_status == OrderStatus.PARTIAL_PAID:
                amount_paid = total_price * Decimal(str(random.uniform(0.3, 0.8)))
            elif order_status == OrderStatus.CANCELLED:
                amount_paid = Decimal('0') if random.random() > 0.3 else total_price * Decimal(str(random.uniform(0.1, 0.5)))
            else:  # PENDING
                amount_paid = Decimal('0')
            
            amount_paid = amount_paid.quantize(Decimal('0.01'))
            
            # 创建订单
            order_create = OrderCreate(
                customer_id=customer.id,
                salesperson_id=salesperson.id,
                amount_paid=amount_paid,
                payment_method=random.choice(payment_methods) if amount_paid > 0 else None,
                remark=f"订单{i+1}备注" if random.random() > 0.6 else None,
                created_at=order_date,
                items=items
            )
            
            # 保存到数据库
            from app.crud.crud_order import order
            db_order = order.create_with_items(db, obj_in=order_create)
            
            # 更新订单状态
            if order_status != OrderStatus.PENDING:
                db_order.status = order_status
                db.commit()
            
            created_orders += 1
            
            if (i + 1) % 10 == 0:
                print(f"   已生成 {i + 1}/{count} 条记录...")
                
        except Exception as e:
            print(f"❌ 生成第{i+1}条记录时出错: {e}")
            db.rollback()
            continue
    
    print(f"✅ 成功生成 {created_orders} 条销售记录")
    return created_orders


def main():
    """主函数"""
    print("🚀 销售记录数据生成器")
    print("=" * 50)
    
    db = SessionLocal()
    try:
        # 检查数据库连接
        db.execute(text("SELECT 1"))
        print("✅ 数据库连接正常")
        
        # 生成销售记录
        count = generate_sales_records(db, 40)
        
        if count > 0:
            print("\n" + "=" * 50)
            print("🎉 销售记录生成完成！")
            print(f"📊 生成统计:")
            print(f"   总记录数: {count}")
            
            # 统计信息
            total_orders = db.query(Order).count()
            total_amount = db.query(func.sum(Order.total_price)).scalar() or 0
            paid_amount = db.query(func.sum(Order.amount_paid)).scalar() or 0
            
            print(f"   数据库总订单: {total_orders}")
            print(f"   总销售额: ¥{total_amount:.2f}")
            print(f"   已收金额: ¥{paid_amount:.2f}")
            
            print("\n🔗 相关API接口:")
            print("   GET /api/v1/orders/ - 查看订单列表")
            print("   GET /api/v1/statistics/sales-summary - 销售统计")
            print("   GET /api/v1/statistics/top-customers - 客户排行")
        else:
            print("\n❌ 没有生成任何记录")
            
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False
    finally:
        db.close()
    
    return True


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
