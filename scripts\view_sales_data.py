#!/usr/bin/env python3
"""
查看销售记录数据
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text, func, desc
from app.db.database import SessionLocal
from app.db.models import User, Customer, RabbitType, Order, OrderItem, OrderStatus


def view_recent_orders(db, limit=10):
    """查看最近的订单"""
    print(f"📋 最近{limit}条订单:")
    print("-" * 80)
    
    orders = db.query(Order).order_by(desc(Order.created_at)).limit(limit).all()
    
    for i, order in enumerate(orders, 1):
        customer = db.query(Customer).filter(Customer.id == order.customer_id).first()
        salesperson = db.query(User).filter(User.id == order.salesperson_id).first()
        
        print(f"{i:2d}. 订单#{order.id}")
        print(f"    客户: {customer.name if customer else '未知'}")
        print(f"    销售员: {salesperson.username if salesperson else '未知'}")
        print(f"    总价: ¥{order.total_price:.2f}")
        print(f"    已付: ¥{order.amount_paid:.2f}")
        print(f"    状态: {order.status.value}")
        print(f"    时间: {order.created_at.strftime('%Y-%m-%d %H:%M')}")
        
        # 显示订单明细
        items = db.query(OrderItem).filter(OrderItem.order_id == order.id).all()
        if items:
            print(f"    明细:")
            for item in items:
                rabbit_type = db.query(RabbitType).filter(RabbitType.id == item.rabbit_type_id).first()
                type_name = rabbit_type.name if rabbit_type else '未知类型'
                print(f"      - {type_name}: {item.quantity}{item.unit} × ¥{item.unit_price:.2f} = ¥{item.quantity * item.unit_price:.2f}")
        print()


def view_sales_statistics(db):
    """查看销售统计"""
    print("📊 销售统计:")
    print("-" * 50)
    
    # 总体统计
    total_orders = db.query(Order).count()
    total_amount = db.query(func.sum(Order.total_price)).scalar() or 0
    paid_amount = db.query(func.sum(Order.amount_paid)).scalar() or 0
    
    print(f"总订单数: {total_orders}")
    print(f"总销售额: ¥{total_amount:.2f}")
    print(f"已收金额: ¥{paid_amount:.2f}")
    print(f"待收金额: ¥{total_amount - paid_amount:.2f}")
    print()
    
    # 按状态统计
    print("按状态统计:")
    for status in OrderStatus:
        count = db.query(Order).filter(Order.status == status).count()
        amount = db.query(func.sum(Order.total_price)).filter(Order.status == status).scalar() or 0
        print(f"  {status.value}: {count}单, ¥{amount:.2f}")
    print()
    
    # 销售员排行
    print("销售员排行:")
    salesperson_stats = db.query(
        User.username,
        func.count(Order.id).label('order_count'),
        func.sum(Order.total_price).label('total_amount')
    ).join(Order, User.id == Order.salesperson_id)\
     .group_by(User.id, User.username)\
     .order_by(desc('total_amount')).all()
    
    for i, (username, count, amount) in enumerate(salesperson_stats, 1):
        print(f"  {i}. {username}: {count}单, ¥{amount:.2f}")
    print()
    
    # 客户排行
    print("客户排行:")
    customer_stats = db.query(
        Customer.name,
        func.count(Order.id).label('order_count'),
        func.sum(Order.total_price).label('total_amount')
    ).join(Order, Customer.id == Order.customer_id)\
     .group_by(Customer.id, Customer.name)\
     .order_by(desc('total_amount')).limit(10).all()
    
    for i, (name, count, amount) in enumerate(customer_stats, 1):
        print(f"  {i}. {name}: {count}单, ¥{amount:.2f}")
    print()


def view_product_statistics(db):
    """查看产品销售统计"""
    print("🐰 产品销售统计:")
    print("-" * 50)
    
    product_stats = db.query(
        RabbitType.name,
        func.sum(OrderItem.quantity).label('total_quantity'),
        func.sum(OrderItem.quantity * OrderItem.unit_price).label('total_amount'),
        func.count(OrderItem.id).label('order_count')
    ).join(OrderItem, RabbitType.id == OrderItem.rabbit_type_id)\
     .group_by(RabbitType.id, RabbitType.name)\
     .order_by(desc('total_amount')).all()
    
    for i, (name, quantity, amount, count) in enumerate(product_stats, 1):
        print(f"{i:2d}. {name}")
        print(f"    销量: {quantity}")
        print(f"    销售额: ¥{amount:.2f}")
        print(f"    订单数: {count}")
        print()


def view_monthly_trend(db):
    """查看月度销售趋势"""
    print("📈 月度销售趋势:")
    print("-" * 50)
    
    monthly_stats = db.execute(text("""
        SELECT 
            DATE_FORMAT(created_at, '%Y-%m') as month,
            COUNT(*) as order_count,
            SUM(total_price) as total_amount,
            SUM(amount_paid) as paid_amount
        FROM orders 
        GROUP BY DATE_FORMAT(created_at, '%Y-%m')
        ORDER BY month DESC
        LIMIT 6
    """)).fetchall()
    
    for month, count, total, paid in monthly_stats:
        print(f"{month}: {count}单, 销售额¥{total:.2f}, 已收¥{paid:.2f}")


def main():
    """主函数"""
    print("🚀 销售记录数据查看器")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # 检查数据库连接
        db.execute(text("SELECT 1"))
        print("✅ 数据库连接正常\n")
        
        # 查看最近订单
        view_recent_orders(db, 5)
        
        # 查看销售统计
        view_sales_statistics(db)
        
        # 查看产品统计
        view_product_statistics(db)
        
        # 查看月度趋势
        view_monthly_trend(db)
        
        print("=" * 60)
        print("🎉 数据查看完成！")
        print("\n🔗 相关API接口:")
        print("   GET /api/v1/orders/ - 查看订单列表")
        print("   GET /api/v1/orders/{id} - 查看订单详情")
        print("   GET /api/v1/statistics/sales-summary - 销售统计")
        print("   GET /api/v1/statistics/top-customers - 客户排行")
        print("   GET /api/v1/statistics/top-products - 产品排行")
        
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False
    finally:
        db.close()
    
    return True


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
