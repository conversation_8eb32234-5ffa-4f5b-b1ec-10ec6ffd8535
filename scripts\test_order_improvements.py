#!/usr/bin/env python3
"""
测试订单系统改进功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from app.db.database import SessionLocal
from app.core.order_number import generate_order_number, parse_order_number, validate_order_number


def test_order_number_generation(db):
    """测试订单编号生成"""
    print("🔢 测试订单编号生成...")
    
    try:
        # 生成几个订单编号
        numbers = []
        for i in range(5):
            number = generate_order_number(db)
            numbers.append(number)
            print(f"   生成编号 {i+1}: {number}")
        
        # 验证编号唯一性
        unique_numbers = set(numbers)
        if len(unique_numbers) == len(numbers):
            print("✅ 所有编号都是唯一的")
        else:
            print("❌ 发现重复编号")
            return False
        
        # 验证编号格式
        for number in numbers:
            if validate_order_number(number):
                parsed = parse_order_number(number)
                print(f"   {number} -> {parsed['type']}, 日期: {parsed['date_str']}, 序号: {parsed['sequence']}")
            else:
                print(f"❌ 编号格式错误: {number}")
                return False
        
        print("✅ 订单编号生成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 订单编号生成测试失败: {e}")
        return False


def test_order_status_display(db):
    """测试订单状态显示"""
    print("\n📊 测试订单状态统计...")
    
    try:
        # 查询各状态订单数量
        result = db.execute(text("""
            SELECT status, COUNT(*) as count 
            FROM orders 
            GROUP BY status 
            ORDER BY 
                CASE status 
                    WHEN 'pending' THEN 1
                    WHEN 'partial_paid' THEN 2
                    WHEN 'paid' THEN 3
                    WHEN 'deleted' THEN 4
                    ELSE 5
                END
        """))
        
        status_counts = result.fetchall()
        
        status_display = {
            'pending': '待付款',
            'partial_paid': '部分付款',
            'paid': '已付清',
            'deleted': '已删除'
        }
        
        print("📋 当前订单状态分布:")
        total_orders = 0
        for status, count in status_counts:
            display_name = status_display.get(status, status)
            print(f"   {display_name}: {count} 单")
            total_orders += count
        
        print(f"   总计: {total_orders} 单")
        
        # 检查是否还有旧的cancelled状态
        result = db.execute(text("SELECT COUNT(*) FROM orders WHERE status = 'cancelled'"))
        cancelled_count = result.scalar()
        
        if cancelled_count > 0:
            print(f"⚠️ 仍有 {cancelled_count} 个订单状态为'cancelled'")
            return False
        else:
            print("✅ 没有旧的'cancelled'状态订单")
        
        print("✅ 订单状态测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 订单状态测试失败: {e}")
        return False


def test_order_number_uniqueness(db):
    """测试订单编号唯一性"""
    print("\n🔍 测试订单编号唯一性...")
    
    try:
        # 检查是否有重复的订单编号
        result = db.execute(text("""
            SELECT order_number, COUNT(*) as count 
            FROM orders 
            WHERE order_number IS NOT NULL 
            GROUP BY order_number 
            HAVING COUNT(*) > 1
        """))
        
        duplicates = result.fetchall()
        
        if duplicates:
            print(f"❌ 发现 {len(duplicates)} 个重复的订单编号:")
            for number, count in duplicates:
                print(f"   {number}: {count} 次")
            return False
        else:
            print("✅ 所有订单编号都是唯一的")
        
        # 检查编号格式
        result = db.execute(text("""
            SELECT order_number 
            FROM orders 
            WHERE order_number IS NOT NULL 
            AND order_number NOT REGEXP '^DD[0-9]{8}[0-9]{6}$'
            LIMIT 5
        """))
        
        invalid_formats = result.fetchall()
        
        if invalid_formats:
            print(f"❌ 发现 {len(invalid_formats)} 个格式错误的订单编号:")
            for (number,) in invalid_formats:
                print(f"   {number}")
            return False
        else:
            print("✅ 所有订单编号格式正确")
        
        print("✅ 订单编号唯一性测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 订单编号唯一性测试失败: {e}")
        return False


def test_sample_orders(db):
    """显示示例订单"""
    print("\n📋 示例订单信息:")
    
    try:
        # 获取各种状态的示例订单
        result = db.execute(text("""
            SELECT 
                o.id,
                o.order_number,
                o.status,
                o.total_price,
                o.amount_paid,
                o.created_at,
                c.name as customer_name
            FROM orders o
            LEFT JOIN customers c ON o.customer_id = c.id
            ORDER BY o.status, o.created_at DESC
            LIMIT 10
        """))
        
        orders = result.fetchall()
        
        status_display = {
            'pending': '待付款',
            'partial_paid': '部分付款',
            'paid': '已付清',
            'deleted': '已删除'
        }
        
        for order in orders:
            status_name = status_display.get(order.status, order.status)
            print(f"   订单 {order.order_number} | {status_name} | ¥{order.total_price:.2f} | {order.customer_name}")
        
        print("✅ 示例订单显示完成")
        return True
        
    except Exception as e:
        print(f"❌ 示例订单显示失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 订单系统改进功能测试")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # 检查数据库连接
        db.execute(text("SELECT 1"))
        print("✅ 数据库连接正常\n")
        
        # 运行测试
        tests = [
            ("订单编号生成", test_order_number_generation),
            ("订单状态显示", test_order_status_display),
            ("订单编号唯一性", test_order_number_uniqueness),
            ("示例订单", test_sample_orders),
        ]
        
        success_count = 0
        for test_name, test_func in tests:
            print(f"🧪 运行测试: {test_name}")
            if test_func(db):
                success_count += 1
            print()
        
        # 总结
        print("=" * 60)
        print(f"📊 测试结果: {success_count}/{len(tests)} 通过")
        
        if success_count == len(tests):
            print("🎉 所有测试通过！订单系统改进功能正常")
            
            print("\n📋 改进功能总结:")
            print("   ✅ 订单编号: DD + 日期 + 6位序号")
            print("   ✅ 订单状态: 已取消 -> 已删除")
            print("   ✅ 软删除: 数据保留，状态标记")
            print("   ✅ 超级管理员: 可删除和恢复订单")
            
            print("\n🔗 新增API功能:")
            print("   - 订单列表显示订单编号")
            print("   - 软删除订单（超级管理员）")
            print("   - 恢复已删除订单（超级管理员）")
            print("   - 查看已删除订单列表（超级管理员）")
            
        else:
            print("❌ 部分测试失败，请检查问题")
            
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        return False
    finally:
        db.close()
    
    return True


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
