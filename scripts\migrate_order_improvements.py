#!/usr/bin/env python3
"""
订单系统改进迁移脚本
1. 添加订单编号字段
2. 更新订单状态（取消 -> 删除）
3. 为现有订单生成订单编号
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from app.db.database import SessionLocal
from app.core.order_number import generate_order_number


def add_order_number_column(db):
    """添加订单编号字段"""
    print("📋 添加订单编号字段...")
    
    try:
        # 检查字段是否已存在
        result = db.execute(text("""
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'orders' AND COLUMN_NAME = 'order_number'
        """))
        
        if result.fetchone():
            print("✅ 订单编号字段已存在，跳过添加")
            return True
        
        # 添加订单编号字段
        db.execute(text("""
            ALTER TABLE orders 
            ADD COLUMN order_number VARCHAR(32) 
            COMMENT '订单编号'
        """))
        
        db.commit()
        print("✅ 订单编号字段添加成功")
        return True
        
    except Exception as e:
        print(f"❌ 添加订单编号字段失败: {e}")
        db.rollback()
        return False


def update_order_status_enum(db):
    """更新订单状态枚举"""
    print("📋 更新订单状态枚举...")
    
    try:
        # 检查是否有cancelled状态的订单
        result = db.execute(text("""
            SELECT COUNT(*) FROM orders WHERE status = 'cancelled'
        """))
        cancelled_count = result.scalar()
        
        if cancelled_count > 0:
            print(f"📊 发现 {cancelled_count} 个已取消的订单，将更新为已删除状态")
            
            # 更新cancelled状态为deleted
            db.execute(text("""
                UPDATE orders SET status = 'deleted' WHERE status = 'cancelled'
            """))
            
            print(f"✅ 已将 {cancelled_count} 个订单状态从'已取消'更新为'已删除'")
        
        # 更新枚举定义（如果数据库支持）
        try:
            db.execute(text("""
                ALTER TABLE orders 
                MODIFY COLUMN status 
                ENUM('pending', 'partial_paid', 'paid', 'deleted') 
                NOT NULL DEFAULT 'pending' 
                COMMENT '订单状态'
            """))
            print("✅ 订单状态枚举更新成功")
        except Exception as enum_error:
            print(f"⚠️ 枚举更新警告: {enum_error}")
            print("   (这可能是正常的，取决于数据库版本)")
        
        db.commit()
        return True
        
    except Exception as e:
        print(f"❌ 更新订单状态失败: {e}")
        db.rollback()
        return False


def generate_order_numbers_for_existing(db):
    """为现有订单生成订单编号"""
    print("📋 为现有订单生成订单编号...")
    
    try:
        # 获取没有订单编号的订单
        result = db.execute(text("""
            SELECT id, created_at 
            FROM orders 
            WHERE order_number IS NULL OR order_number = ''
            ORDER BY created_at ASC
        """))
        
        orders_without_number = result.fetchall()
        
        if not orders_without_number:
            print("✅ 所有订单都已有订单编号")
            return True
        
        print(f"📊 发现 {len(orders_without_number)} 个订单需要生成编号")
        
        # 按日期分组生成编号
        date_counters = {}
        updated_count = 0
        
        for order_id, created_at in orders_without_number:
            # 获取订单日期
            order_date = created_at.date() if created_at else datetime.now().date()
            date_str = order_date.strftime("%Y%m%d")
            
            # 初始化或递增当日计数器
            if date_str not in date_counters:
                # 查询当日已有的订单数量
                existing_count = db.execute(text("""
                    SELECT COUNT(*) 
                    FROM orders 
                    WHERE order_number LIKE :pattern
                """), {"pattern": f"DD{date_str}%"}).scalar()
                date_counters[date_str] = existing_count
            
            date_counters[date_str] += 1
            
            # 生成订单编号
            sequence = f"{date_counters[date_str]:06d}"
            order_number = f"DD{date_str}{sequence}"
            
            # 确保唯一性
            while True:
                check_result = db.execute(text("""
                    SELECT COUNT(*) FROM orders WHERE order_number = :order_number
                """), {"order_number": order_number}).scalar()
                
                if check_result == 0:
                    break
                
                date_counters[date_str] += 1
                sequence = f"{date_counters[date_str]:06d}"
                order_number = f"DD{date_str}{sequence}"
            
            # 更新订单编号
            db.execute(text("""
                UPDATE orders SET order_number = :order_number WHERE id = :order_id
            """), {"order_number": order_number, "order_id": order_id})
            
            updated_count += 1
            
            if updated_count % 10 == 0:
                print(f"   已处理 {updated_count}/{len(orders_without_number)} 个订单...")
        
        # 添加唯一索引
        try:
            db.execute(text("""
                ALTER TABLE orders ADD UNIQUE INDEX idx_order_number (order_number)
            """))
            print("✅ 订单编号唯一索引创建成功")
        except Exception as index_error:
            print(f"⚠️ 索引创建警告: {index_error}")
            print("   (索引可能已存在)")
        
        db.commit()
        print(f"✅ 成功为 {updated_count} 个订单生成编号")
        return True
        
    except Exception as e:
        print(f"❌ 生成订单编号失败: {e}")
        db.rollback()
        return False


def verify_migration(db):
    """验证迁移结果"""
    print("\n🔍 验证迁移结果...")
    
    try:
        # 检查订单编号字段
        result = db.execute(text("""
            SELECT COUNT(*) FROM orders WHERE order_number IS NULL OR order_number = ''
        """))
        empty_numbers = result.scalar()
        
        if empty_numbers > 0:
            print(f"⚠️ 仍有 {empty_numbers} 个订单没有编号")
        else:
            print("✅ 所有订单都有编号")
        
        # 检查订单状态
        result = db.execute(text("""
            SELECT status, COUNT(*) as count 
            FROM orders 
            GROUP BY status 
            ORDER BY status
        """))
        
        status_counts = result.fetchall()
        print("📊 订单状态统计:")
        for status, count in status_counts:
            status_display = {
                'pending': '待付款',
                'partial_paid': '部分付款',
                'paid': '已付清',
                'deleted': '已删除',
                'cancelled': '已取消(旧)'
            }.get(status, status)
            print(f"   {status_display}: {count}")
        
        # 检查订单编号格式
        result = db.execute(text("""
            SELECT order_number 
            FROM orders 
            WHERE order_number NOT REGEXP '^DD[0-9]{8}[0-9]{6}$'
            LIMIT 5
        """))
        
        invalid_numbers = result.fetchall()
        if invalid_numbers:
            print(f"⚠️ 发现 {len(invalid_numbers)} 个格式不正确的订单编号:")
            for (number,) in invalid_numbers:
                print(f"   {number}")
        else:
            print("✅ 所有订单编号格式正确")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 订单系统改进迁移")
    print("=" * 50)
    
    db = SessionLocal()
    try:
        # 检查数据库连接
        db.execute(text("SELECT 1"))
        print("✅ 数据库连接正常\n")
        
        # 执行迁移步骤
        steps = [
            ("添加订单编号字段", add_order_number_column),
            ("更新订单状态枚举", update_order_status_enum),
            ("生成现有订单编号", generate_order_numbers_for_existing),
        ]
        
        success_count = 0
        for step_name, step_func in steps:
            print(f"🔄 执行: {step_name}")
            if step_func(db):
                success_count += 1
                print(f"✅ {step_name} 完成\n")
            else:
                print(f"❌ {step_name} 失败\n")
                break
        
        # 验证结果
        if success_count == len(steps):
            verify_migration(db)
            
            print("\n" + "=" * 50)
            print("🎉 订单系统改进迁移完成！")
            print("\n📋 改进内容:")
            print("   ✅ 添加了订单编号字段")
            print("   ✅ 订单状态：已取消 -> 已删除")
            print("   ✅ 为现有订单生成了编号")
            print("   ✅ 支持软删除功能")
            
            print("\n🔗 新增API接口:")
            print("   DELETE /api/v1/orders/{id} - 软删除订单（超级管理员）")
            print("   POST /api/v1/orders/{id}/restore - 恢复订单（超级管理员）")
            print("   GET /api/v1/orders/deleted/ - 查看已删除订单（超级管理员）")
            
        else:
            print("\n❌ 迁移未完全成功，请检查错误信息")
            
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        return False
    finally:
        db.close()
    
    return True


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
