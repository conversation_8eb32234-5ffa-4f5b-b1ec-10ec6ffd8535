"""
销售订单API端点
"""

from typing import Any, List, Optional
from decimal import Decimal
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from app.api import deps
from app.crud.crud_order import order
from app.crud.crud_customer import customer
from app.crud.crud_user import user
from app.crud.crud_rabbit_type import rabbit_type
from app.db.database import get_db
from app.db.models import User, OrderStatus
from app.schemas.order import (
    OrderCreate, OrderUpdate, OrderResponse, OrderSummary,
    OrderItemResponse, get_status_display
)
from app.core.permissions import (
    require_permission, Permission, can_access_order_data, has_permission
)

router = APIRouter()


def build_order_response(db_order) -> OrderResponse:
    """构建订单响应数据"""
    # 构建订单明细
    items = []
    for item in db_order.items:
        items.append(OrderItemResponse(
            id=item.id,
            rabbit_type_id=item.rabbit_type_id,
            rabbit_type_name=item.rabbit_type.name,
            quantity=item.quantity,
            unit=item.unit,
            unit_price=item.unit_price,
            total_price=item.total_price,
            remark=item.remark
        ))

    return OrderResponse(
        id=db_order.id,
        customer_id=db_order.customer_id,
        customer_name=db_order.customer.name,
        salesperson_id=db_order.salesperson_id,
        salesperson_name=db_order.salesperson.username,
        total_price=db_order.total_price,
        amount_paid=db_order.amount_paid,
        unpaid_amount=db_order.unpaid_amount,
        payment_method=db_order.payment_method,
        status=db_order.status,
        status_display=get_status_display(db_order.status),
        remark=db_order.remark,
        items=items
    )


@router.get("/", response_model=List[OrderSummary])
def read_orders(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    customer_id: Optional[int] = Query(None, description="按客户ID过滤"),
    salesperson_id: Optional[int] = Query(None, description="按销售员ID过滤"),
    status: Optional[OrderStatus] = Query(None, description="按订单状态过滤"),
    search: Optional[str] = Query(None, description="搜索客户或销售员姓名"),
    order_by: str = Query("created_at", description="排序字段"),
    current_user: User = Depends(require_permission(Permission.ORDER_READ)),
) -> Any:
    """
    获取订单列表
    支持分页、过滤、搜索和排序
    """
    # 如果用户没有查看所有订单的权限，只能查看自己的订单
    if not has_permission(current_user, Permission.ORDER_READ_ALL):
        salesperson_id = current_user.id

    if search:
        orders = order.search_orders(db, search_term=search, skip=skip, limit=limit)
    else:
        orders = order.get_multi_with_details(
            db,
            skip=skip,
            limit=limit,
            customer_id=customer_id,
            salesperson_id=salesperson_id,
            status=status,
            order_by=order_by
        )

    # 构建响应数据
    result = []
    for db_order in orders:
        result.append(OrderSummary(
            id=db_order.id,
            order_number=db_order.order_number,
            customer_id=db_order.customer_id,
            customer_name=db_order.customer.name,
            salesperson_id=db_order.salesperson_id,
            salesperson_name=db_order.salesperson.username,
            total_price=db_order.total_price,
            amount_paid=db_order.amount_paid,
            unpaid_amount=db_order.unpaid_amount,
            status=db_order.status,
            status_display=get_status_display(db_order.status),
            created_at=db_order.created_at,
            item_count=len(db_order.items) if db_order.items else 0
        ))

    return result


@router.post("/", response_model=OrderResponse)
def create_order(
    *,
    db: Session = Depends(get_db),
    order_in: OrderCreate,
    current_user: User = Depends(require_permission(Permission.ORDER_CREATE)),
) -> Any:
    """
    创建销售订单
    """
    # 验证客户是否存在
    db_customer = customer.get(db, id=order_in.customer_id)
    if not db_customer:
        raise HTTPException(status_code=404, detail="客户不存在")

    # 验证销售员是否存在
    db_salesperson = user.get(db, id=order_in.salesperson_id)
    if not db_salesperson:
        raise HTTPException(status_code=404, detail="销售员不存在")

    # 验证兔子类型是否存在
    for item in order_in.items:
        db_rabbit_type = rabbit_type.get(db, id=item.rabbit_type_id)
        if not db_rabbit_type:
            raise HTTPException(
                status_code=404, 
                detail=f"兔子类型ID {item.rabbit_type_id} 不存在"
            )

    # 创建订单
    db_order = order.create_with_items(db, obj_in=order_in)
    
    # 获取完整的订单信息
    db_order = order.get_with_details(db, id=db_order.id)
    
    return build_order_response(db_order)


@router.get("/{order_id}", response_model=OrderResponse)
def read_order(
    *,
    db: Session = Depends(get_db),
    order_id: int,
    current_user: User = Depends(require_permission(Permission.ORDER_READ)),
) -> Any:
    """
    根据ID获取订单详情
    """
    db_order = order.get_with_details(db, id=order_id)
    if not db_order:
        raise HTTPException(status_code=404, detail="订单不存在")

    # 检查用户是否有权限访问这个订单
    if not can_access_order_data(current_user, db_order.salesperson_id):
        raise HTTPException(status_code=403, detail="无权限访问此订单")

    return build_order_response(db_order)


@router.put("/{order_id}", response_model=OrderResponse)
def update_order(
    *,
    db: Session = Depends(get_db),
    order_id: int,
    order_in: OrderUpdate,
    current_user: User = Depends(require_permission(Permission.ORDER_UPDATE)),
) -> Any:
    """
    更新订单信息
    """
    db_order = order.get(db, id=order_id)
    if not db_order:
        raise HTTPException(status_code=404, detail="订单不存在")

    # 验证客户和销售员（如果有更新）
    if order_in.customer_id:
        db_customer = customer.get(db, id=order_in.customer_id)
        if not db_customer:
            raise HTTPException(status_code=404, detail="客户不存在")

    if order_in.salesperson_id:
        db_salesperson = user.get(db, id=order_in.salesperson_id)
        if not db_salesperson:
            raise HTTPException(status_code=404, detail="销售员不存在")

    updated_order = order.update(db, db_obj=db_order, obj_in=order_in)
    updated_order = order.get_with_details(db, id=updated_order.id)
    
    return build_order_response(updated_order)


@router.post("/{order_id}/payment")
def add_payment(
    *,
    db: Session = Depends(get_db),
    order_id: int,
    additional_payment: Decimal,
    payment_method: Optional[str] = None,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    添加订单付款
    """
    if additional_payment <= 0:
        raise HTTPException(status_code=400, detail="付款金额必须大于0")

    updated_order = order.update_payment(
        db, 
        order_id=order_id, 
        additional_payment=additional_payment,
        payment_method=payment_method
    )
    
    if not updated_order:
        raise HTTPException(status_code=404, detail="订单不存在")

    updated_order = order.get_with_details(db, id=updated_order.id)
    return build_order_response(updated_order)


@router.get("/unpaid/list", response_model=List[OrderSummary])
def read_unpaid_orders(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取有欠款的订单列表
    """
    orders = order.get_unpaid_orders(db, skip=skip, limit=limit)
    
    result = []
    for db_order in orders:
        result.append(OrderSummary(
            id=db_order.id,
            order_number=db_order.order_number,
            customer_id=db_order.customer_id,
            customer_name=db_order.customer.name,
            salesperson_id=db_order.salesperson_id,
            salesperson_name=db_order.salesperson.username,
            total_price=db_order.total_price,
            amount_paid=db_order.amount_paid,
            unpaid_amount=db_order.unpaid_amount,
            status=db_order.status,
            status_display=get_status_display(db_order.status),
            created_at=db_order.created_at,
            item_count=len(db_order.items) if db_order.items else 0
        ))

    return result


@router.delete("/{order_id}")
def soft_delete_order(
    *,
    db: Session = Depends(get_db),
    order_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    软删除订单（仅超级管理员）
    将订单状态改为已删除，而不是物理删除
    """
    try:
        db_order = order.soft_delete(db=db, order_id=order_id, user=current_user)
        return {
            "message": "订单删除成功",
            "order_id": db_order.id,
            "order_number": db_order.order_number,
            "status": db_order.status.value
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/{order_id}/restore")
def restore_order(
    *,
    db: Session = Depends(get_db),
    order_id: int,
    current_user: User = Depends(deps.get_current_active_superuser),
) -> Any:
    """
    恢复已删除的订单（仅超级管理员）
    """
    try:
        db_order = order.restore_deleted(db=db, order_id=order_id, user=current_user)
        return {
            "message": "订单恢复成功",
            "order_id": db_order.id,
            "order_number": db_order.order_number,
            "status": db_order.status.value
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/deleted/", response_model=List[OrderSummary])
def get_deleted_orders(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = Query(default=100, le=100),
    current_user: User = Depends(deps.get_current_active_superuser),
) -> Any:
    """
    获取已删除的订单列表（仅超级管理员）
    """
    deleted_orders = order.get_deleted_orders(db, skip=skip, limit=limit)

    # 构建响应数据
    result = []
    for db_order in deleted_orders:
        # 获取客户信息
        db_customer = customer.get(db, id=db_order.customer_id)
        customer_name = db_customer.name if db_customer else "未知客户"

        # 获取销售员信息
        db_salesperson = user.get(db, id=db_order.salesperson_id)
        salesperson_name = db_salesperson.username if db_salesperson else "未知销售员"

        order_summary = OrderSummary(
            id=db_order.id,
            order_number=db_order.order_number,
            customer_id=db_order.customer_id,
            customer_name=customer_name,
            salesperson_id=db_order.salesperson_id,
            salesperson_name=salesperson_name,
            total_price=db_order.total_price,
            amount_paid=db_order.amount_paid,
            unpaid_amount=db_order.unpaid_amount,
            status=db_order.status,
            status_display=get_status_display(db_order.status),
            created_at=db_order.created_at,
            item_count=len(db_order.items) if db_order.items else 0
        )
        result.append(order_summary)

    return result
