#!/usr/bin/env python3
"""
测试新的订单编号格式
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.db.database import SessionLocal
from app.core.order_number import (
    generate_order_number, 
    parse_order_number, 
    validate_order_number,
    get_daily_order_count,
    get_next_sequence_for_date
)


def test_order_number_generation():
    """测试订单编号生成"""
    print("🔢 测试订单编号生成...")
    
    db = SessionLocal()
    try:
        # 生成今天的订单编号
        today_numbers = []
        for i in range(3):
            number = generate_order_number(db)
            today_numbers.append(number)
            print(f"   生成编号 {i+1}: {number}")
        
        # 验证编号格式
        for number in today_numbers:
            if validate_order_number(number):
                parsed = parse_order_number(number)
                date_str = parsed['date_str']
                sequence = parsed['sequence']
                formatted_date = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
                print(f"   {number} -> {formatted_date} 第{sequence}单")
            else:
                print(f"❌ 编号格式错误: {number}")
                return False
        
        # 测试指定日期的编号生成
        test_date = datetime(2024, 12, 25)  # 圣诞节
        christmas_number = generate_order_number(db, test_date)
        print(f"   圣诞节订单: {christmas_number}")
        
        parsed = parse_order_number(christmas_number)
        if parsed['valid']:
            print(f"   解析结果: {parsed['date'].strftime('%Y-%m-%d')} 第{parsed['sequence']}单")
        
        print("✅ 订单编号生成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 订单编号生成测试失败: {e}")
        return False
    finally:
        db.close()


def test_order_number_parsing():
    """测试订单编号解析"""
    print("\n🔍 测试订单编号解析...")
    
    test_numbers = [
        "20241201001",  # 正常格式
        "20241225999",  # 大序号
        "20240229001",  # 闰年
        "TH20241201001",  # 退货单
        "HH20241201001",  # 换货单
        "2024120100",   # 错误格式（太短）
        "202412010001", # 错误格式（太长）
        "abcd1201001",  # 错误格式（非数字）
    ]
    
    for number in test_numbers:
        result = parse_order_number(number)
        if result['valid']:
            date_str = result['date_str']
            sequence = result['sequence']
            order_type = result['type']
            formatted_date = f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
            print(f"   ✅ {number} -> {order_type}, {formatted_date}, 第{sequence}单")
        else:
            print(f"   ❌ {number} -> {result['error']}")
    
    print("✅ 订单编号解析测试完成")
    return True


def test_daily_statistics():
    """测试每日统计功能"""
    print("\n📊 测试每日统计功能...")
    
    db = SessionLocal()
    try:
        # 测试今天的订单数量
        today = datetime.now()
        today_count = get_daily_order_count(db, today)
        print(f"   今天订单数量: {today_count}")
        
        # 测试下一个序号
        next_seq = get_next_sequence_for_date(db, today)
        print(f"   今天下一个序号: {next_seq}")
        
        # 测试几个历史日期
        test_dates = [
            datetime(2025, 7, 30),
            datetime(2025, 7, 29),
            datetime(2025, 7, 28),
        ]
        
        for test_date in test_dates:
            count = get_daily_order_count(db, test_date)
            next_seq = get_next_sequence_for_date(db, test_date)
            date_str = test_date.strftime("%Y-%m-%d")
            print(f"   {date_str}: {count}单, 下一个序号: {next_seq}")
        
        print("✅ 每日统计测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 每日统计测试失败: {e}")
        return False
    finally:
        db.close()


def test_edge_cases():
    """测试边界情况"""
    print("\n🧪 测试边界情况...")
    
    db = SessionLocal()
    try:
        # 测试跨年
        new_year = datetime(2025, 1, 1)
        ny_number = generate_order_number(db, new_year)
        print(f"   新年第一单: {ny_number}")
        
        # 测试闰年2月29日
        leap_day = datetime(2024, 2, 29)
        leap_number = generate_order_number(db, leap_day)
        print(f"   闰年2月29日: {leap_number}")
        
        # 测试年末
        year_end = datetime(2024, 12, 31)
        ye_number = generate_order_number(db, year_end)
        print(f"   年末最后一天: {ye_number}")
        
        # 验证所有编号
        test_numbers = [ny_number, leap_number, ye_number]
        for number in test_numbers:
            if validate_order_number(number):
                parsed = parse_order_number(number)
                date_obj = parsed['date']
                print(f"   ✅ {number} -> {date_obj.strftime('%Y-%m-%d %A')}")
            else:
                print(f"   ❌ {number} 格式错误")
                return False
        
        print("✅ 边界情况测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 边界情况测试失败: {e}")
        return False
    finally:
        db.close()


def show_format_comparison():
    """显示格式对比"""
    print("\n📋 新旧格式对比:")
    print("   旧格式: DD20241201000001 (16位)")
    print("   新格式: 20241201001 (11位)")
    print()
    print("   优势:")
    print("   ✅ 更简洁: 减少5个字符")
    print("   ✅ 更直观: 直接看到日期")
    print("   ✅ 更实用: 去掉无意义前缀")
    print("   ✅ 更易读: 日期+序号清晰")
    print()
    print("   示例对比:")
    examples = [
        ("DD20241201000001", "20241201001", "2024-12-01 第1单"),
        ("DD20241201000015", "20241201015", "2024-12-01 第15单"),
        ("DD20241225000001", "20241225001", "2024-12-25 第1单"),
    ]
    
    for old, new, meaning in examples:
        print(f"   {old} -> {new} ({meaning})")


def main():
    """主函数"""
    print("🚀 新订单编号格式测试")
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("订单编号生成", test_order_number_generation),
        ("订单编号解析", test_order_number_parsing),
        ("每日统计功能", test_daily_statistics),
        ("边界情况", test_edge_cases),
    ]
    
    success_count = 0
    for test_name, test_func in tests:
        print(f"🧪 运行测试: {test_name}")
        if test_func():
            success_count += 1
        print()
    
    # 显示格式对比
    show_format_comparison()
    
    # 总结
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {success_count}/{len(tests)} 通过")
    
    if success_count == len(tests):
        print("🎉 所有测试通过！新订单编号格式工作正常")
        
        print("\n📋 新格式总结:")
        print("   格式: YYYYMMDDXXX")
        print("   长度: 11位")
        print("   含义: 销售日期 + 当天序号")
        print("   示例: 20241201001 = 2024年12月1日第1单")
        
        print("\n🔗 支持功能:")
        print("   ✅ 自动生成唯一编号")
        print("   ✅ 按日期自增序号")
        print("   ✅ 编号格式验证")
        print("   ✅ 编号解析功能")
        print("   ✅ 每日统计功能")
        print("   ✅ 退货单/换货单支持")
        
    else:
        print("❌ 部分测试失败，请检查问题")
    
    return success_count == len(tests)


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
