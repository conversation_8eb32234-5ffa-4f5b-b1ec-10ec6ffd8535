"""
订单编号生成器
"""

import time
import random
from datetime import datetime
from typing import Optional
from sqlalchemy.orm import Session
from sqlalchemy import text


def generate_order_number(db: Session, prefix: str = "DD") -> str:
    """
    生成唯一的订单编号
    
    格式: DD + YYYYMMDD + 6位序号
    例如: DD20241201000001
    
    Args:
        db: 数据库会话
        prefix: 订单前缀，默认为"DD"（订单）
        
    Returns:
        str: 唯一的订单编号
    """
    # 获取当前日期
    today = datetime.now()
    date_str = today.strftime("%Y%m%d")
    
    # 构建今日订单编号前缀
    today_prefix = f"{prefix}{date_str}"
    
    # 查询今日订单数量来确定序号
    count_query = text("""
        SELECT COUNT(*)
        FROM orders
        WHERE order_number LIKE :pattern
    """)

    result = db.execute(count_query, {"pattern": f"{today_prefix}%"}).scalar()
    new_sequence = (result or 0) + 1
    
    # 格式化为6位序号
    sequence_str = f"{new_sequence:06d}"
    
    # 生成完整订单编号
    order_number = f"{today_prefix}{sequence_str}"
    
    # 验证唯一性（防止并发问题）
    max_attempts = 10
    attempts = 0
    
    while attempts < max_attempts:
        # 检查是否已存在
        check_query = text("SELECT COUNT(*) FROM orders WHERE order_number = :order_number")
        count = db.execute(check_query, {"order_number": order_number}).scalar()
        
        if count == 0:
            return order_number
        
        # 如果存在，序号加1重试
        new_sequence += 1
        sequence_str = f"{new_sequence:06d}"
        order_number = f"{today_prefix}{sequence_str}"
        attempts += 1
    
    # 如果重试多次仍失败，添加随机后缀
    random_suffix = random.randint(100, 999)
    return f"{today_prefix}{new_sequence:06d}{random_suffix}"


def generate_return_number(db: Session) -> str:
    """
    生成退货单编号
    
    格式: TH + YYYYMMDD + 6位序号
    例如: TH20241201000001
    """
    return generate_order_number(db, prefix="TH")


def generate_exchange_number(db: Session) -> str:
    """
    生成换货单编号
    
    格式: HH + YYYYMMDD + 6位序号
    例如: HH20241201000001
    """
    return generate_order_number(db, prefix="HH")


def parse_order_number(order_number: str) -> dict:
    """
    解析订单编号
    
    Args:
        order_number: 订单编号
        
    Returns:
        dict: 包含前缀、日期、序号等信息
    """
    if len(order_number) < 16:
        return {"valid": False, "error": "订单编号长度不正确"}
    
    try:
        prefix = order_number[:2]
        date_str = order_number[2:10]
        sequence_str = order_number[10:16]
        
        # 验证日期格式
        date_obj = datetime.strptime(date_str, "%Y%m%d")
        
        # 验证序号格式
        sequence = int(sequence_str)
        
        return {
            "valid": True,
            "prefix": prefix,
            "date": date_obj,
            "date_str": date_str,
            "sequence": sequence,
            "sequence_str": sequence_str,
            "type": get_order_type_by_prefix(prefix)
        }
    except Exception as e:
        return {"valid": False, "error": f"解析失败: {str(e)}"}


def get_order_type_by_prefix(prefix: str) -> str:
    """
    根据前缀获取订单类型
    """
    type_map = {
        "DD": "销售订单",
        "TH": "退货单",
        "HH": "换货单"
    }
    return type_map.get(prefix, "未知类型")


def validate_order_number(order_number: str) -> bool:
    """
    验证订单编号格式是否正确
    
    Args:
        order_number: 订单编号
        
    Returns:
        bool: 是否有效
    """
    result = parse_order_number(order_number)
    return result.get("valid", False)


def get_next_sequence_for_date(db: Session, date: datetime, prefix: str = "DD") -> int:
    """
    获取指定日期的下一个序号
    
    Args:
        db: 数据库会话
        date: 指定日期
        prefix: 订单前缀
        
    Returns:
        int: 下一个序号
    """
    date_str = date.strftime("%Y%m%d")
    today_prefix = f"{prefix}{date_str}"
    
    query = text("""
        SELECT COUNT(*) 
        FROM orders 
        WHERE order_number LIKE :pattern
    """)
    
    count = db.execute(query, {"pattern": f"{today_prefix}%"}).scalar()
    return count + 1


def get_daily_order_count(db: Session, date: Optional[datetime] = None, prefix: str = "DD") -> int:
    """
    获取指定日期的订单数量
    
    Args:
        db: 数据库会话
        date: 指定日期，默认为今天
        prefix: 订单前缀
        
    Returns:
        int: 订单数量
    """
    if date is None:
        date = datetime.now()
    
    date_str = date.strftime("%Y%m%d")
    today_prefix = f"{prefix}{date_str}"
    
    query = text("""
        SELECT COUNT(*) 
        FROM orders 
        WHERE order_number LIKE :pattern
    """)
    
    return db.execute(query, {"pattern": f"{today_prefix}%"}).scalar() or 0
