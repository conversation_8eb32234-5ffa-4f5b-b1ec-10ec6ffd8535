"""
订单编号生成器
"""

import time
import random
from datetime import datetime
from typing import Optional
from sqlalchemy.orm import Session
from sqlalchemy import text


def generate_order_number(db: Session, sale_date: Optional[datetime] = None) -> str:
    """
    生成唯一的订单编号

    格式: YYYYMMDD + 3位序号
    例如: 20241201001 (2024年12月1日第1单)

    Args:
        db: 数据库会话
        sale_date: 销售日期，默认为当前日期

    Returns:
        str: 唯一的订单编号
    """
    # 获取销售日期
    if sale_date is None:
        sale_date = datetime.now()

    date_str = sale_date.strftime("%Y%m%d")

    # 使用数据库锁来防止并发问题
    # 查询当天最大序号
    max_seq_query = text("""
        SELECT MAX(CAST(SUBSTRING(order_number, 9, 3) AS UNSIGNED)) as max_seq
        FROM orders
        WHERE order_number LIKE :pattern
        FOR UPDATE
    """)

    result = db.execute(max_seq_query, {"pattern": f"{date_str}%"}).scalar()
    new_sequence = (result or 0) + 1

    # 格式化为3位序号
    sequence_str = f"{new_sequence:03d}"

    # 生成完整订单编号
    order_number = f"{date_str}{sequence_str}"

    # 最终验证唯一性
    check_query = text("SELECT COUNT(*) FROM orders WHERE order_number = :order_number")
    count = db.execute(check_query, {"order_number": order_number}).scalar()

    if count > 0:
        # 如果仍然冲突，使用时间戳后缀
        import time
        timestamp_suffix = int(time.time() * 1000) % 1000
        new_sequence = 900 + (timestamp_suffix % 99)
        sequence_str = f"{new_sequence:03d}"
        order_number = f"{date_str}{sequence_str}"

    return order_number


def generate_return_number(db: Session, return_date: Optional[datetime] = None) -> str:
    """
    生成退货单编号

    格式: TH + YYYYMMDD + 3位序号
    例如: TH20241201001
    """
    if return_date is None:
        return_date = datetime.now()

    date_str = return_date.strftime("%Y%m%d")

    # 查询当天已有的退货单数量
    count_query = text("""
        SELECT COUNT(*)
        FROM orders
        WHERE order_number LIKE :pattern
    """)

    result = db.execute(count_query, {"pattern": f"TH{date_str}%"}).scalar()
    new_sequence = (result or 0) + 1
    sequence_str = f"{new_sequence:03d}"

    return f"TH{date_str}{sequence_str}"


def generate_exchange_number(db: Session, exchange_date: Optional[datetime] = None) -> str:
    """
    生成换货单编号

    格式: HH + YYYYMMDD + 3位序号
    例如: HH20241201001
    """
    if exchange_date is None:
        exchange_date = datetime.now()

    date_str = exchange_date.strftime("%Y%m%d")

    # 查询当天已有的换货单数量
    count_query = text("""
        SELECT COUNT(*)
        FROM orders
        WHERE order_number LIKE :pattern
    """)

    result = db.execute(count_query, {"pattern": f"HH{date_str}%"}).scalar()
    new_sequence = (result or 0) + 1
    sequence_str = f"{new_sequence:03d}"

    return f"HH{date_str}{sequence_str}"


def parse_order_number(order_number: str) -> dict:
    """
    解析订单编号

    Args:
        order_number: 订单编号

    Returns:
        dict: 包含日期、序号等信息
    """
    # 检查是否是退货单或换货单
    if order_number.startswith("TH") or order_number.startswith("HH"):
        if len(order_number) != 13:  # TH/HH + 8位日期 + 3位序号
            return {"valid": False, "error": "退货/换货单编号长度不正确"}

        try:
            prefix = order_number[:2]
            date_str = order_number[2:10]
            sequence_str = order_number[10:13]

            # 验证日期格式
            date_obj = datetime.strptime(date_str, "%Y%m%d")

            # 验证序号格式
            sequence = int(sequence_str)

            return {
                "valid": True,
                "prefix": prefix,
                "date": date_obj,
                "date_str": date_str,
                "sequence": sequence,
                "sequence_str": sequence_str,
                "type": get_order_type_by_prefix(prefix)
            }
        except Exception as e:
            return {"valid": False, "error": f"解析失败: {str(e)}"}

    # 普通销售订单
    if len(order_number) != 11:  # 8位日期 + 3位序号
        return {"valid": False, "error": "订单编号长度不正确"}

    try:
        date_str = order_number[:8]
        sequence_str = order_number[8:11]

        # 验证日期格式
        date_obj = datetime.strptime(date_str, "%Y%m%d")

        # 验证序号格式
        sequence = int(sequence_str)

        return {
            "valid": True,
            "prefix": "",
            "date": date_obj,
            "date_str": date_str,
            "sequence": sequence,
            "sequence_str": sequence_str,
            "type": "销售订单"
        }
    except Exception as e:
        return {"valid": False, "error": f"解析失败: {str(e)}"}


def get_order_type_by_prefix(prefix: str) -> str:
    """
    根据前缀获取订单类型
    """
    type_map = {
        "": "销售订单",
        "TH": "退货单",
        "HH": "换货单"
    }
    return type_map.get(prefix, "未知类型")


def validate_order_number(order_number: str) -> bool:
    """
    验证订单编号格式是否正确

    Args:
        order_number: 订单编号

    Returns:
        bool: 是否有效
    """
    result = parse_order_number(order_number)
    return result.get("valid", False)


def get_next_sequence_for_date(db: Session, date: datetime) -> int:
    """
    获取指定日期的下一个序号

    Args:
        db: 数据库会话
        date: 指定日期

    Returns:
        int: 下一个序号
    """
    date_str = date.strftime("%Y%m%d")

    query = text("""
        SELECT COUNT(*)
        FROM orders
        WHERE order_number LIKE :pattern
    """)

    count = db.execute(query, {"pattern": f"{date_str}%"}).scalar()
    return (count or 0) + 1


def get_daily_order_count(db: Session, date: Optional[datetime] = None) -> int:
    """
    获取指定日期的订单数量

    Args:
        db: 数据库会话
        date: 指定日期，默认为今天

    Returns:
        int: 订单数量
    """
    if date is None:
        date = datetime.now()

    date_str = date.strftime("%Y%m%d")

    query = text("""
        SELECT COUNT(*)
        FROM orders
        WHERE order_number LIKE :pattern
    """)

    return db.execute(query, {"pattern": f"{date_str}%"}).scalar() or 0
