# 兔场管理系统 API 完整文档

## 📋 目录
- [快速开始](#快速开始)
- [认证系统](#认证系统)
- [用户管理](#用户管理)
- [客户管理](#客户管理)
- [兔子类型管理](#兔子类型管理)
- [销售订单管理](#销售订单管理)
- [数据统计](#数据统计)
- [Token管理](#token管理)
- [前端集成](#前端集成)
- [错误处理](#错误处理)

## 🚀 快速开始

### 基础信息
- **API地址**: `http://localhost:8000`
- **API前缀**: `/api/v1`
- **认证方式**: JWT Bearer Token
- **CORS**: 已完全开放，支持所有来源访问 ✅

### Token配置
- **Access Token有效期**: 8小时 (28800秒)
- **Refresh Token有效期**: 7天
- **适用场景**: 日常办公使用，减少频繁登录

### 权限控制
- **认证方式**: 基于角色的访问控制（RBAC）
- **角色类型**: 超级管理员、普通管理员、销售员、饲养员
- **权限验证**: 每个API端点都有相应的权限要求

### 测试账户
- **手机号**: `13800138000`
- **密码**: `admin123`
- **角色**: 超级管理员（拥有所有权限）

### API文档
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## 🔐 认证系统

### 1. 用户登录

**接口**: `POST /api/v1/auth/login`

**请求体**:
```json
{
  "phone": "13800138000",
  "password": "admin123"
}
```

**响应**:
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 28800
}
```

### 2. 刷新Token

**接口**: `POST /api/v1/auth/refresh`

**请求体**:
```json
{
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**响应**: 同登录响应格式

### 3. 退出登录

**接口**: `POST /api/v1/auth/logout`

**请求头**:
```
Authorization: Bearer <access_token>
```

**响应**:
```json
{
  "message": "退出登录成功",
  "user_id": 1,
  "username": "测试用户"
}
```

### 4. 获取验证码

**接口**: `GET /api/v1/auth/captcha`

**响应**:
```json
{
  "captcha_id": "uuid-string",
  "captcha_image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
}
```

## 👥 用户管理

### 1. 获取当前用户信息

**接口**: `GET /api/v1/users/me`

**请求头**:
```
Authorization: Bearer <access_token>
```

**响应**:
```json
{
  "id": 1,
  "phone": "13800138000",
  "username": "测试用户",
  "role": "admin",
  "is_active": true,
  "is_superuser": false
}
```

### 2. 获取用户列表（管理员）

**接口**: `GET /api/v1/users/`

**权限**: 需要管理员权限

**查询参数**:
- `skip`: 跳过的记录数（默认：0）
- `limit`: 返回的记录数（默认：100）

### 3. 创建新用户（管理员）

**接口**: `POST /api/v1/users/`

**权限**: 需要管理员权限

**请求体**:
```json
{
  "phone": "15000150000",
  "username": "新用户",
  "password": "password123",
  "role": "user",
  "is_active": true,
  "is_superuser": false
}
```

## 👤 客户管理

### 1. 获取客户列表

**接口**: `GET /api/v1/customers/`

**请求头**:
```
Authorization: Bearer <access_token>
```

**查询参数**:
- `skip`: 跳过的记录数（默认：0）
- `limit`: 返回的记录数（默认：100）
- `order_by`: 排序字段（name, phone, created_at, updated_at）
- `search_name`: 按姓名搜索
- `search_phone`: 按电话搜索

**响应**:
```json
[
  {
    "id": 1,
    "name": "张三",
    "phone": "13912345678",
    "address": "北京市朝阳区某某街道",
    "remark": "老客户，信誉良好"
  }
]
```

### 2. 创建客户

**接口**: `POST /api/v1/customers/`

**请求体**:
```json
{
  "name": "张三",
  "phone": "13912345678",
  "address": "北京市朝阳区某某街道",
  "remark": "老客户，信誉良好"
}
```

### 3. 获取单个客户

**接口**: `GET /api/v1/customers/{customer_id}`

### 4. 更新客户

**接口**: `PUT /api/v1/customers/{customer_id}`

**请求体**:
```json
{
  "name": "张三",
  "phone": "13912345678",
  "address": "北京市朝阳区新地址123号",
  "remark": "老客户，信誉良好，已搬家"
}
```

### 5. 删除客户

**接口**: `DELETE /api/v1/customers/{customer_id}`

**响应**:
```json
{
  "message": "客户删除成功"
}
```

**错误响应**（有订单关联时）:
```json
{
  "detail": "无法删除该客户，因为有 3 个订单记录关联此客户。 相关订单ID: 1, 2, 3。请先处理相关订单。"
}
```

**说明**: 如果客户有关联的订单记录，将无法删除，需要先删除相关订单。

## 🐇 兔子类型管理

### 1. 获取兔子类型列表

**接口**: `GET /api/v1/rabbit-types/`

**查询参数**:
- `skip`: 跳过的记录数（默认：0）
- `limit`: 返回的记录数（默认：100）
- `order_by`: 排序字段（name, created_at, updated_at）
- `search_name`: 按名称搜索

**响应**:
```json
[
  {
    "id": 1,
    "name": "满月兔",
    "default_unit": "只",
    "default_price": 25.00,
    "remark": "出生满一个月的小兔子"
  }
]
```

### 2. 获取所有兔子类型（下拉选择用）

**接口**: `GET /api/v1/rabbit-types/all`

### 3. 创建兔子类型

**接口**: `POST /api/v1/rabbit-types/`

**请求体**:
```json
{
  "name": "满月兔",
  "default_unit": "只",
  "default_price": 25.00,
  "remark": "出生满一个月的小兔子"
}
```

**字段说明**:
- `name`: 兔子类型名称（必填）
- `default_unit`: 默认单位（可选），支持：只、斤、公斤、kg
- `default_price`: 默认单价（可选），范围：0-99999.99元
- `remark`: 备注信息（可选）

### 4. 获取单个兔子类型

**接口**: `GET /api/v1/rabbit-types/{rabbit_type_id}`

**响应**:
```json
{
  "id": 1,
  "name": "满月兔",
  "default_unit": "只",
  "default_price": 25.00,
  "remark": "出生满一个月的小兔子"
}
```

### 5. 更新兔子类型

**接口**: `PUT /api/v1/rabbit-types/{rabbit_type_id}`

**请求体**:
```json
{
  "name": "满月兔",
  "default_unit": "斤",
  "default_price": 28.00,
  "remark": "出生满一个月的小兔子，更新后的描述"
}
```

**说明**: 所有字段都是可选的，只更新提供的字段

### 6. 删除兔子类型

**接口**: `DELETE /api/v1/rabbit-types/{rabbit_type_id}`

**响应**:
```json
{
  "message": "兔子类型删除成功"
}
```

**错误响应**（有订单明细关联时）:
```json
{
  "detail": "无法删除该兔子类型，因为有 2 个订单明细正在使用此类型。 相关订单ID: 3。请先处理相关订单。"
}
```

**说明**: 如果兔子类型被订单明细引用，将无法删除，需要先删除相关订单。

### 默认值功能说明

**设计目的**: 提高订单录入效率，减少手动输入错误

**默认单位**:
- 支持的单位：只、斤、公斤、kg
- 用途：创建订单时自动填充单位
- 验证：只能使用预定义的单位

**默认单价**:
- 范围：0-99999.99元
- 用途：创建订单时自动填充单价
- 验证：不能为负数

**使用场景**:
```javascript
// 1. 获取兔子类型（包含默认值）
const rabbitType = await api.get('/rabbit-types/1');
// 返回: { id: 1, name: "满月兔", default_unit: "只", default_price: 25.00 }

// 2. 创建订单时使用默认值
const orderItem = {
  rabbit_type_id: rabbitType.id,
  quantity: 10,
  unit: rabbitType.default_unit || "只",        // 使用默认单位
  unit_price: rabbitType.default_price || 30,   // 使用默认单价
  remark: "使用默认值"
};
```

**业务价值**:
- ✅ 减少录入时间：自动填充常用值
- ✅ 减少错误：标准化单位和价格
- ✅ 提升体验：前端可以预填充表单
- ✅ 灵活性：仍可手动修改具体订单的值

## 🛒 销售订单管理

### 1. 创建销售订单

**接口**: `POST /api/v1/orders/`

**请求体**:
```json
{
  "customer_id": 1,
  "salesperson_id": 2,
  "payment_method": "微信",
  "amount_paid": 500,
  "remark": "部分付款，余款下次结清",
  "created_at": "2025-01-15T14:30:00",
  "items": [
    {
      "rabbit_type_id": 1,
      "quantity": 10,
      "unit": "只",
      "unit_price": 30,
      "remark": "种兔"
    },
    {
      "rabbit_type_id": 2,
      "quantity": 20.5,
      "unit": "斤",
      "unit_price": 18,
      "remark": "淘汰兔"
    }
  ]
}
```

**字段说明**:
- `created_at`: 可选字段，指定订单创建时间（ISO格式）
  - 不指定时使用当前时间
  - 用于补录历史销售数据
  - 不能是未来时间
  - 不能早于2020年

**响应**:
```json
{
  "id": 1,
  "customer_id": 1,
  "customer_name": "张三",
  "salesperson_id": 2,
  "salesperson_name": "李四",
  "total_price": 669.00,
  "amount_paid": 500.00,
  "unpaid_amount": 169.00,
  "payment_method": "微信",
  "status": "partial_paid",
  "status_display": "部分付款",
  "remark": "部分付款，余款下次结清",
  "items": [
    {
      "id": 1,
      "rabbit_type_id": 1,
      "rabbit_type_name": "满月兔",
      "quantity": 10.00,
      "unit": "只",
      "unit_price": 30.00,
      "total_price": 300.00,
      "remark": "种兔"
    }
  ]
}
```

### 2. 获取订单列表

**接口**: `GET /api/v1/orders/`

**查询参数**:
- `skip`: 跳过的记录数（默认：0）
- `limit`: 返回的记录数（默认：100）
- `customer_id`: 按客户ID过滤
- `salesperson_id`: 按销售员ID过滤
- `status`: 按订单状态过滤（pending, partial_paid, paid, deleted）
- `search`: 搜索客户或销售员姓名
- `order_by`: 排序字段（created_at, total_price, customer_name, salesperson_name）

**响应**:
```json
[
  {
    "id": 1,
    "customer_id": 1,
    "customer_name": "张三",
    "salesperson_id": 2,
    "salesperson_name": "李四",
    "total_price": 669.00,
    "amount_paid": 500.00,
    "unpaid_amount": 169.00,
    "status": "partial_paid",
    "status_display": "部分付款",
    "created_at": "2024-01-01T12:00:00Z"
  }
]
```

### 3. 获取单个订单详情

**接口**: `GET /api/v1/orders/{order_id}`

**响应**: 同创建订单的响应格式，包含完整的订单信息和明细

### 4. 更新订单信息

**接口**: `PUT /api/v1/orders/{order_id}`

**请求体**:
```json
{
  "customer_id": 1,
  "salesperson_id": 2,
  "amount_paid": 600,
  "payment_method": "支付宝",
  "status": "partial_paid",
  "remark": "更新后的备注"
}
```

### 5. 添加订单付款

**接口**: `POST /api/v1/orders/{order_id}/payment`

**查询参数**:
- `additional_payment`: 追加付款金额（必需）
- `payment_method`: 付款方式（可选）

**示例**:
```
POST /api/v1/orders/1/payment?additional_payment=300&payment_method=支付宝
```

### 6. 获取欠款订单

**接口**: `GET /api/v1/orders/unpaid/list`

**查询参数**:
- `skip`: 跳过的记录数（默认：0）
- `limit`: 返回的记录数（默认：100）

### 7. 删除订单

**接口**: `DELETE /api/v1/orders/{order_id}`

**响应**:
```json
{
  "message": "订单删除成功"
}
```

### 订单状态说明

| 状态值 | 显示名称 | 说明 |
|--------|----------|------|
| PENDING | 待付款 | 未付任何款项 |
| PARTIAL_PAID | 部分付款 | 已付部分款项，仍有欠款 |
| PAID | 已付清 | 已付清全部款项 |
| DELETED | 已删除 | 订单已删除（仅超级管理员可操作） |

### 业务逻辑说明

1. **自动计算**: 系统自动计算订单总价（各明细小计之和）和未付金额
2. **状态更新**: 根据付款情况自动更新订单状态
3. **数据验证**: 验证客户、销售员、兔子类型是否存在
4. **事务处理**: 订单和明细使用事务方式同时创建，保证数据一致性

## 📊 数据统计

### 设计理念

**为什么在后端做统计？**
- ✅ **性能优秀**: 数据库层面的聚合查询，比前端计算快数百倍
- ✅ **网络优化**: 只传输统计结果，而不是原始数据
- ✅ **权限安全**: 按用户角色返回对应的统计数据
- ✅ **扩展性强**: 支持大数据量，不受前端内存限制

### 1. 订单统计摘要

**接口**: `GET /api/v1/statistics/orders/summary`

**查询参数**:
- `start_date`: 开始日期 (YYYY-MM-DD)
- `end_date`: 结束日期 (YYYY-MM-DD)
- `salesperson_id`: 销售员ID

**响应**:
```json
{
  "summary": {
    "total_orders": 15,
    "total_amount": 12500.00,
    "paid_amount": 10000.00,
    "unpaid_amount": 2500.00
  },
  "status_breakdown": {
    "pending": {
      "count": 3,
      "total_amount": 1500.00,
      "paid_amount": 0.00
    },
    "partial_paid": {
      "count": 5,
      "total_amount": 5000.00,
      "paid_amount": 3000.00
    },
    "paid": {
      "count": 7,
      "total_amount": 6000.00,
      "paid_amount": 6000.00
    }
  },
  "salesperson_breakdown": [
    {
      "salesperson_id": 1,
      "salesperson_name": "张三",
      "order_count": 8,
      "total_amount": 7500.00,
      "paid_amount": 6000.00,
      "unpaid_amount": 1500.00
    }
  ],
  "period": {
    "start_date": "2025-01-01",
    "end_date": "2025-01-31"
  }
}
```

### 2. 订单趋势分析

**接口**: `GET /api/v1/statistics/orders/trends`

**查询参数**:
- `days`: 统计天数 (默认30天)
- `salesperson_id`: 销售员ID

**响应**:
```json
{
  "trends": [
    {
      "date": "2025-01-01",
      "order_count": 3,
      "total_amount": 1500.00,
      "paid_amount": 1200.00,
      "unpaid_amount": 300.00
    }
  ],
  "period": {
    "start_date": "2025-01-01",
    "end_date": "2025-01-30",
    "days": 30
  }
}
```

### 3. 产品销售排行

**接口**: `GET /api/v1/statistics/products/ranking`

**查询参数**:
- `start_date`: 开始日期
- `end_date`: 结束日期
- `limit`: 返回数量 (默认10)

**响应**:
```json
{
  "ranking": [
    {
      "rank": 1,
      "product_id": 1,
      "product_name": "满月兔",
      "total_quantity": 150.5,
      "total_amount": 4500.00,
      "order_count": 12
    }
  ],
  "period": {
    "start_date": "2025-01-01",
    "end_date": "2025-01-31"
  }
}
```

### 4. 客户消费排行

**接口**: `GET /api/v1/statistics/customers/ranking`

**查询参数**:
- `start_date`: 开始日期
- `end_date`: 结束日期
- `limit`: 返回数量 (默认10)

**响应**:
```json
{
  "ranking": [
    {
      "rank": 1,
      "customer_id": 1,
      "customer_name": "张三",
      "customer_phone": "13912345678",
      "order_count": 8,
      "total_amount": 5000.00,
      "paid_amount": 4500.00,
      "unpaid_amount": 500.00
    }
  ],
  "period": {
    "start_date": "2025-01-01",
    "end_date": "2025-01-31"
  }
}
```

### 权限控制

- **销售员**: 只能查看自己的统计数据
- **管理员**: 可以查看所有统计数据
- **数据过滤**: 自动根据用户权限过滤数据

### 性能优势对比

| 方案 | 数据传输 | 计算位置 | 性能 | 扩展性 |
|------|----------|----------|------|--------|
| **前端统计** | 传输所有原始数据 | 前端JavaScript | ❌ 慢 | ❌ 差 |
| **后端统计** | 只传输统计结果 | 数据库聚合查询 | ✅ 快 | ✅ 好 |

**示例对比**:
- 前端方案: 传输1000个订单数据 (~500KB) + 前端计算时间
- 后端方案: 传输统计结果 (~2KB) + 数据库毫秒级查询

## � 权限管理

### 角色和权限体系

| 角色 | 中文名称 | 主要权限 |
|------|----------|----------|
| super_admin | 超级管理员 | 所有权限，包括用户管理 |
| admin | 普通管理员 | 除用户创建/修改/删除外的所有权限 |
| salesperson | 销售员 | 客户管理、订单管理、兔子类型查看 |
| breeder | 饲养员 | 兔子类型管理、客户查看、订单查看 |

### 权限列表

#### 用户管理权限
- `user:read` - 查看用户信息
- `user:create` - 创建用户（仅超级管理员）
- `user:update` - 修改用户信息（仅超级管理员）
- `user:delete` - 删除用户（仅超级管理员）

#### 客户管理权限
- `customer:read` - 查看客户信息
- `customer:create` - 创建客户
- `customer:update` - 修改客户信息
- `customer:delete` - 删除客户（销售员无此权限）

#### 兔子类型管理权限
- `rabbit_type:read` - 查看兔子类型
- `rabbit_type:create` - 创建兔子类型
- `rabbit_type:update` - 修改兔子类型
- `rabbit_type:delete` - 删除兔子类型

#### 订单管理权限
- `order:read` - 查看订单
- `order:create` - 创建订单
- `order:update` - 修改订单
- `order:delete` - 删除订单（销售员无此权限）
- `order:read_all` - 查看所有人的订单（销售员只能看自己的）
- `order:payment` - 订单付款

### 1. 获取我的权限信息

**接口**: `GET /api/v1/permissions/my-permissions`

**请求头**:
```
Authorization: Bearer <access_token>
```

**响应**:
```json
{
  "user_id": 1,
  "username": "测试用户",
  "role": {
    "code": "admin",
    "name": "普通管理员"
  },
  "permissions": [
    {
      "code": "customer:read",
      "description": "查看客户信息"
    }
  ],
  "total_permissions": 15
}
```

### 2. 检查指定权限

**接口**: `GET /api/v1/permissions/check/{permission}`

**示例**: `GET /api/v1/permissions/check/customer:create`

**响应**:
```json
{
  "user_id": 1,
  "permission": "customer:create",
  "permission_description": "创建客户",
  "has_permission": true,
  "user_role": "admin"
}
```

### 3. 获取所有角色和权限（管理员）

**接口**: `GET /api/v1/permissions/roles`

**权限要求**: 管理员权限

**响应**:
```json
{
  "roles": {
    "admin": {
      "name": "普通管理员",
      "permissions": [...]
    }
  },
  "total_roles": 4,
  "available_permissions": [...]
}
```

### 权限控制说明

1. **API端点保护**: 每个API端点都有相应的权限要求
2. **角色继承**: 高级角色包含低级角色的权限
3. **数据隔离**: 销售员只能查看自己创建的订单
4. **403错误**: 无权限时返回403 Forbidden错误
5. **权限检查**: 提供API检查用户是否有指定权限

## �🔑 Token管理

### Token配置优化
- **Access Token**: 8小时有效期，适合工作日使用
- **Refresh Token**: 7天有效期，支持长期免登录
- **自动刷新**: 提前5分钟自动刷新，无感知体验
- **安全机制**: 退出登录黑名单机制

### 前端Token管理
```javascript
// 登录并保存token
const login = async (phone, password) => {
  const response = await fetch('/api/v1/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ phone, password })
  });

  if (response.ok) {
    const data = await response.json();
    localStorage.setItem('access_token', data.access_token);
    localStorage.setItem('refresh_token', data.refresh_token);
    localStorage.setItem('token_expires_at', Date.now() + data.expires_in * 1000);
    return data;
  }
  throw new Error('登录失败');
};

// 智能获取有效token
const getAuthHeaders = async () => {
  let token = localStorage.getItem('access_token');
  const expiresAt = localStorage.getItem('token_expires_at');

  // 检查是否需要刷新（提前5分钟）
  if (expiresAt && Date.now() > (parseInt(expiresAt) - 5 * 60 * 1000)) {
    try {
      const refreshToken = localStorage.getItem('refresh_token');
      const response = await fetch('/api/v1/auth/refresh', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ refresh_token: refreshToken })
      });

      if (response.ok) {
        const data = await response.json();
        localStorage.setItem('access_token', data.access_token);
        localStorage.setItem('refresh_token', data.refresh_token);
        localStorage.setItem('token_expires_at', Date.now() + data.expires_in * 1000);
        token = data.access_token;
      } else {
        // 刷新失败，跳转登录
        localStorage.clear();
        window.location.href = '/login';
        return null;
      }
    } catch (error) {
      localStorage.clear();
      window.location.href = '/login';
      return null;
    }
  }

  return {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  };
};
```

## 💻 前端集成

### JavaScript API客户端
```javascript
class ApiClient {
  constructor(baseURL = 'http://localhost:8000/api/v1') {
    this.baseURL = baseURL;
  }

  async request(endpoint, options = {}) {
    const headers = await getAuthHeaders();
    if (!headers) return null;

    const url = `${this.baseURL}${endpoint}`;
    const config = { headers, ...options };

    const response = await fetch(url, config);

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || '请求失败');
    }

    return response.json();
  }

  // GET请求
  get(endpoint, params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const url = queryString ? `${endpoint}?${queryString}` : endpoint;
    return this.request(url);
  }

  // POST请求
  post(endpoint, data) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }

  // PUT请求
  put(endpoint, data) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data)
    });
  }

  // DELETE请求
  delete(endpoint) {
    return this.request(endpoint, { method: 'DELETE' });
  }
}

// 使用示例
const api = new ApiClient();

// 获取客户列表
const customers = await api.get('/customers/', { search_name: '张' });

// 创建订单
const newOrder = await api.post('/orders/', {
  customer_id: 1,
  salesperson_id: 2,
  items: [...]
});
```

### React Hook示例
```jsx
import { useState, useEffect } from 'react';

export const useApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const apiCall = async (apiFunction) => {
    setLoading(true);
    setError(null);

    try {
      const result = await apiFunction();
      return result;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return { apiCall, loading, error };
};

// 使用示例
const OrderList = () => {
  const [orders, setOrders] = useState([]);
  const { apiCall, loading, error } = useApi();

  useEffect(() => {
    const loadOrders = async () => {
      const result = await apiCall(() => api.get('/orders/'));
      setOrders(result);
    };
    loadOrders();
  }, []);

  if (loading) return <div>加载中...</div>;
  if (error) return <div>错误: {error}</div>;

  return (
    <div>
      {orders.map(order => (
        <div key={order.id}>{order.customer_name}</div>
      ))}
    </div>
  );
};
```

## ⚠️ 错误处理

### 常见HTTP状态码

| 状态码 | 说明 | 处理方式 |
|--------|------|----------|
| 200 | 请求成功 | 正常处理响应数据 |
| 400 | 请求参数错误或业务约束 | 检查请求参数格式或处理业务约束 |
| 401 | 未授权 | 重新登录或刷新token |
| 403 | 权限不足 | 提示用户权限不足 |
| 404 | 资源不存在 | 提示资源不存在 |
| 422 | 数据验证失败 | 显示具体验证错误信息 |
| 500 | 服务器内部错误 | 提示系统错误，稍后重试 |

### 常见业务约束错误（400状态码）

#### 删除约束错误
- **客户删除**: 如果客户有关联订单，无法删除
- **兔子类型删除**: 如果兔子类型被订单明细引用，无法删除
- **错误信息**: 包含具体的关联数据信息和相关ID

#### 唯一性约束错误
- **客户手机号**: 手机号已存在时无法创建
- **兔子类型名称**: 类型名称已存在时无法创建

### 错误响应格式
```json
{
  "detail": "具体错误信息"
}
```

### 前端错误处理示例
```javascript
const handleApiError = (error, response) => {
  switch (response.status) {
    case 401:
      // Token过期，重新登录
      localStorage.clear();
      window.location.href = '/login';
      break;
    case 400:
    case 422:
      // 参数错误，显示错误信息
      alert(`请求错误: ${error.detail}`);
      break;
    case 404:
      alert('请求的资源不存在');
      break;
    case 500:
      alert('系统错误，请稍后重试');
      break;
    default:
      alert(`未知错误: ${error.detail || '请求失败'}`);
  }
};
```

## 📝 常用API调用示例

### 完整的销售流程
```javascript
// 1. 登录
const tokenData = await login('13800138000', 'admin123');

// 2. 获取客户列表
const customers = await api.get('/customers/');

// 3. 获取兔子类型
const rabbitTypes = await api.get('/rabbit-types/all');

// 4. 创建销售订单（当前时间）
const order = await api.post('/orders/', {
  customer_id: customers[0].id,
  salesperson_id: tokenData.user_id,
  payment_method: "微信",
  amount_paid: 500,
  items: [
    {
      rabbit_type_id: rabbitTypes[0].id,
      quantity: 10,
      unit: "只",
      unit_price: 30,
      remark: "种兔"
    }
  ]
});

// 4.1 创建历史订单（指定时间）
const historicalOrder = await api.post('/orders/', {
  customer_id: customers[0].id,
  salesperson_id: tokenData.user_id,
  payment_method: "现金",
  amount_paid: 800,
  created_at: "2025-01-15T14:30:00", // 指定历史时间
  items: [
    {
      rabbit_type_id: rabbitTypes[0].id,
      quantity: 15,
      unit: "只",
      unit_price: 35,
      remark: "历史补录订单"
    }
  ]
});

// 5. 添加付款
const updatedOrder = await api.post(`/orders/${order.id}/payment?additional_payment=169&payment_method=支付宝`);

// 6. 查看订单详情
const orderDetail = await api.get(`/orders/${order.id}`);
```

### 数据查询示例
```javascript
// 按客户查询订单
const customerOrders = await api.get('/orders/', { customer_id: 1 });

// 搜索客户
const searchResults = await api.get('/customers/', { search_name: '张' });

// 获取欠款订单
const unpaidOrders = await api.get('/orders/unpaid/list');

// 按销售员查询订单
const salesOrders = await api.get('/orders/', { salesperson_id: 2 });
```

## 🎯 最佳实践

### 1. 认证管理
- ✅ 使用localStorage存储token
- ✅ 实现自动token刷新机制
- ✅ 处理token过期情况
- ✅ 退出时清除所有token

### 2. 错误处理
- ✅ 统一的错误处理函数
- ✅ 用户友好的错误提示
- ✅ 网络错误重试机制
- ✅ 日志记录和错误上报

### 3. 性能优化
- ✅ 使用分页查询避免大量数据
- ✅ 实现数据缓存机制
- ✅ 防抖搜索功能
- ✅ 懒加载和虚拟滚动

### 4. 用户体验
- ✅ 加载状态提示
- ✅ 操作成功/失败反馈
- ✅ 表单验证和提示
- ✅ 快捷键支持

## 📚 相关资源

### 在线文档
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

### 示例代码
- **前端演示**: `frontend-examples/`
- **测试脚本**: `scripts/`

### 配置文件
- **环境配置**: `.env`
- **依赖管理**: `requirements.txt`

---

## 📞 技术支持

如有问题，请查看：
1. **API文档**: http://localhost:8000/docs
2. **项目README**: README.md
3. **状态文档**: STATUS.md

**注意**: 本文档涵盖了所有API接口的完整使用方法，建议收藏备用！
