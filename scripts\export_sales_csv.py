#!/usr/bin/env python3
"""
导出销售记录为CSV文件
"""

import sys
import os
import csv
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from app.db.database import SessionLocal
from app.db.models import User, Customer, RabbitType, Order, OrderItem


def export_sales_to_csv(db, filename="sales_records.csv"):
    """导出销售记录到CSV文件"""
    print(f"📊 导出销售记录到 {filename}...")
    
    # 查询所有订单及相关信息
    query = text("""
        SELECT 
            o.id as order_id,
            o.created_at,
            c.name as customer_name,
            c.phone as customer_phone,
            c.address as customer_address,
            u.username as salesperson,
            o.total_price,
            o.amount_paid,
            o.status,
            o.payment_method,
            o.remark as order_remark,
            oi.id as item_id,
            rt.name as product_name,
            oi.quantity,
            oi.unit,
            oi.unit_price,
            oi.quantity * oi.unit_price as item_total,
            oi.remark as item_remark
        FROM orders o
        LEFT JOIN customers c ON o.customer_id = c.id
        LEFT JOIN users u ON o.salesperson_id = u.id
        LEFT JOIN order_items oi ON o.id = oi.order_id
        LEFT JOIN rabbit_types rt ON oi.rabbit_type_id = rt.id
        ORDER BY o.created_at DESC, o.id, oi.id
    """)
    
    results = db.execute(query).fetchall()
    
    # 写入CSV文件
    with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
        fieldnames = [
            '订单ID', '订单时间', '客户姓名', '客户电话', '客户地址', '销售员',
            '订单总价', '已付金额', '订单状态', '付款方式', '订单备注',
            '明细ID', '产品名称', '数量', '单位', '单价', '明细小计', '明细备注'
        ]
        
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        for row in results:
            writer.writerow({
                '订单ID': row.order_id,
                '订单时间': row.created_at.strftime('%Y-%m-%d %H:%M:%S') if row.created_at else '',
                '客户姓名': row.customer_name or '',
                '客户电话': row.customer_phone or '',
                '客户地址': row.customer_address or '',
                '销售员': row.salesperson or '',
                '订单总价': f"{row.total_price:.2f}" if row.total_price else '0.00',
                '已付金额': f"{row.amount_paid:.2f}" if row.amount_paid else '0.00',
                '订单状态': row.status or '',
                '付款方式': row.payment_method or '',
                '订单备注': row.order_remark or '',
                '明细ID': row.item_id or '',
                '产品名称': row.product_name or '',
                '数量': f"{row.quantity}" if row.quantity else '',
                '单位': row.unit or '',
                '单价': f"{row.unit_price:.2f}" if row.unit_price else '',
                '明细小计': f"{row.item_total:.2f}" if row.item_total else '',
                '明细备注': row.item_remark or ''
            })
    
    print(f"✅ 成功导出 {len(results)} 条记录到 {filename}")
    return len(results)


def export_summary_to_csv(db, filename="sales_summary.csv"):
    """导出销售汇总到CSV文件"""
    print(f"📈 导出销售汇总到 {filename}...")
    
    # 按订单汇总
    query = text("""
        SELECT 
            o.id as order_id,
            o.created_at,
            c.name as customer_name,
            u.username as salesperson,
            o.total_price,
            o.amount_paid,
            o.total_price - o.amount_paid as balance,
            o.status,
            o.payment_method,
            COUNT(oi.id) as item_count,
            GROUP_CONCAT(CONCAT(rt.name, '(', oi.quantity, oi.unit, ')') SEPARATOR ', ') as products
        FROM orders o
        LEFT JOIN customers c ON o.customer_id = c.id
        LEFT JOIN users u ON o.salesperson_id = u.id
        LEFT JOIN order_items oi ON o.id = oi.order_id
        LEFT JOIN rabbit_types rt ON oi.rabbit_type_id = rt.id
        GROUP BY o.id
        ORDER BY o.created_at DESC
    """)
    
    results = db.execute(query).fetchall()
    
    # 写入CSV文件
    with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
        fieldnames = [
            '订单ID', '订单时间', '客户姓名', '销售员', '订单总价', '已付金额', 
            '待收金额', '订单状态', '付款方式', '商品数量', '商品明细'
        ]
        
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        for row in results:
            writer.writerow({
                '订单ID': row.order_id,
                '订单时间': row.created_at.strftime('%Y-%m-%d %H:%M:%S') if row.created_at else '',
                '客户姓名': row.customer_name or '',
                '销售员': row.salesperson or '',
                '订单总价': f"{row.total_price:.2f}" if row.total_price else '0.00',
                '已付金额': f"{row.amount_paid:.2f}" if row.amount_paid else '0.00',
                '待收金额': f"{row.balance:.2f}" if row.balance else '0.00',
                '订单状态': row.status or '',
                '付款方式': row.payment_method or '',
                '商品数量': row.item_count or 0,
                '商品明细': row.products or ''
            })
    
    print(f"✅ 成功导出 {len(results)} 条汇总记录到 {filename}")
    return len(results)


def main():
    """主函数"""
    print("🚀 销售记录CSV导出器")
    print("=" * 50)
    
    db = SessionLocal()
    try:
        # 检查数据库连接
        db.execute(text("SELECT 1"))
        print("✅ 数据库连接正常\n")
        
        # 导出详细记录
        detail_count = export_sales_to_csv(db, "sales_records_detail.csv")
        
        # 导出汇总记录
        summary_count = export_summary_to_csv(db, "sales_records_summary.csv")
        
        print("\n" + "=" * 50)
        print("🎉 CSV导出完成！")
        print(f"📊 导出统计:")
        print(f"   详细记录: {detail_count} 条 -> sales_records_detail.csv")
        print(f"   汇总记录: {summary_count} 条 -> sales_records_summary.csv")
        
        print("\n📁 文件说明:")
        print("   sales_records_detail.csv - 包含每个订单明细的完整记录")
        print("   sales_records_summary.csv - 按订单汇总的记录")
        
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False
    finally:
        db.close()
    
    return True


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
